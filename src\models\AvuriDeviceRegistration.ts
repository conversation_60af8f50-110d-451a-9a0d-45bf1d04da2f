// This file is auto-generated. Do not edit manually.

export interface AvuriDeviceRegistration {
  metadata: MetadataType;
  headline: string;
  mainContent: string;
  applicationName: string;
  breadcrumbProductName: string;
  breadcrumbBackIcon: string;
  breadcrumbSeparator: string;
  breadcrumbBackRedirectUrl: string;
  breadcrumbBackRedirectText: string;
  breadcrumb: BreadcrumbType;
  verisoundInProgressDescription: string;
  verisoundInProgressIcon: string;
  verisoundInProgressTitle: string;
  accountSetupInProgressDescription: string;
  accountSetupInProgressIcon: string;
  accountSetupInProgressTitle: string;
  approvedMessage: string;
  checkboxValidation: string;
  goToVerisoundFleet: GoToVerisoundFleetType;
  returnToHome: ReturnToHomeType;
  setupLabel: string;
  title: string;
  termsAndConditionsLabel: string;
  alertCloseIcon: string;
  errorsDefault: string;
}

export interface MetadataType {
  browserTitle: string;
  metaAdditionalTags: MetaAdditionalTagType[];
  openGraphTags: OpenGraphTagType[];
  httpEquivalentTags: HttpEquivalentTagType[];
  linkTags: LinkTagType[];
}

export interface MetaAdditionalTagType {
  property: string;
  content: string;
}

export interface OpenGraphTagType {
  property: string;
  content: string;
}

export interface HttpEquivalentTagType {
  property: string;
  content: string;
}

export interface LinkTagType {
  rel: string;
  href: string;
  as: string;
}

export interface BreadcrumbType {
  isBreadcrumbEnabled: boolean;
  breadcrumbPageTitle: string;
  breadcrumbLink: BreadcrumbLinkType;
}

export interface BreadcrumbLinkType {
  href: string;
  text: string;
}

export interface GoToVerisoundFleetType {
  value: ValueType;
}

export interface ValueType {
  href: URL;
  text: string;
  anchor: string;
  linktype: string;
  target: string;
  class: string;
  title: string;
}

export interface ReturnToHomeType {
  value: ValueType;
}

export interface ValueType {
  href: string;
  text: string;
  anchor: string;
  linktype: string;
  class: string;
  title: string;
}