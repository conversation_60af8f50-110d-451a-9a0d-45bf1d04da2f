import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock SkeletonLoader to avoid SCSS and external dependency issues
jest.mock('../../../Common/SkeletonLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="skeleton-loader-mock">Skeleton Loader</div>,
}));

// Mock EDSIcon and RichText to avoid external dependency issues
jest.mock('cx-dle-component-library', () => ({
  EDSIcon: ({ icon }: { icon: string }) => <span data-testid="eds-icon-mock">{icon}</span>,
  RichText: ({ text }: { text: string }) => <span>{typeof text === 'string' ? text.replace(/<[^>]+>/g, '') : ''}</span>,
}));

import { CapabilitiesAlertContainer, TitleLoaderSection } from '../Component';

describe('CapabilitiesAlertContainer', () => {
  it('renders icon, title, and description', () => {
    render(
      <CapabilitiesAlertContainer
        icon="test-icon"
        title="Test Title"
        description="<p>Test Description</p>"
      />
    );
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    // The mocked RichText strips HTML tags, so only the text remains
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    // Icon and RichText are from external libraries, so we just check for their containers
    expect(screen.getByTestId('eds-icon-mock')).toBeInTheDocument();
  });

  it('renders with correct CSS classes', () => {
    render(
      <CapabilitiesAlertContainer
        icon="warning"
        title="Warning Title"
        description="Warning Description"
      />
    );

    const container = document.querySelector('.capabilities-alert-container');
    expect(container).toBeInTheDocument();

    const titleElement = document.querySelector('.capabilities-status-alert__title');
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveTextContent('Warning Title');
  });

  it('handles plain text description', () => {
    render(
      <CapabilitiesAlertContainer
        icon="info"
        title="Info Title"
        description="Plain text description"
      />
    );

    expect(screen.getByText('Info Title')).toBeInTheDocument();
    expect(screen.getByText('Plain text description')).toBeInTheDocument();
  });

  it('passes icon prop to EDSIcon component', () => {
    render(
      <CapabilitiesAlertContainer
        icon="custom-icon"
        title="Title"
        description="Description"
      />
    );

    const iconElement = screen.getByTestId('eds-icon-mock');
    expect(iconElement).toHaveTextContent('custom-icon');
  });
});

describe('TitleLoaderSection', () => {
  it('renders loader wrappers', () => {
    render(<TitleLoaderSection />);
    // Check for loader-wrapper div
    const loaderWrapper = document.querySelector('.loader-wrapper');
    expect(loaderWrapper).toBeTruthy();
    // Check for at least one mocked SkeletonLoader
    expect(screen.getAllByTestId('skeleton-loader-mock').length).toBeGreaterThan(0);
  });

  it('renders multiple skeleton loaders', () => {
    render(<TitleLoaderSection />);
    // Should render two SkeletonLoader components (one inside loader-wrapper, one for cards)
    const skeletonLoaders = screen.getAllByTestId('skeleton-loader-mock');
    expect(skeletonLoaders).toHaveLength(2);
  });

  it('renders with correct structure', () => {
    render(<TitleLoaderSection />);
    // Check that the component renders without errors
    expect(screen.getAllByTestId('skeleton-loader-mock')).toBeDefined();
    // Check for the loader-wrapper class
    const loaderWrapper = document.querySelector('.loader-wrapper');
    expect(loaderWrapper).toBeInTheDocument();
  });
});
