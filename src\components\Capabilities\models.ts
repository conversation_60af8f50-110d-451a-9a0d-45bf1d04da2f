import { UserInfo } from '../../hooks';
import { ServicesType } from '../../models/Account';
import { Dlesitecommoncontent } from '../../models/Dlesitecommoncontent';

export interface CapabilitiesProps extends ServicesType {
  userInfo?: UserInfo;
  modalityData?: Dlesitecommoncontent;
  userProgressiveRegistrations?: ProgressiveRegistration;
  loading?: boolean;
}

export interface ProgressiveRegistration {
  cdxStatus: string;
  application: string;
}
