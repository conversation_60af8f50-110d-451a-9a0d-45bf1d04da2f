import { Icon } from "cx-dle-component-library/components/EDS/models";
import SkeletonLoader from "../../Common/SkeletonLoader";
import { EDSIcon, RichText } from "cx-dle-component-library";

const cardsLoaderList = [
  {
    rows: [
      {
        className: 'capabilities-loader__wrapper',
        columns: [
          { className: 'capabilities-loader' },
          { className: 'capabilities-loader' },
          { className: 'capabilities-loader' },
        ],
      },
    ],
  },
];

export const CapabilitiesAlertContainer = ({
  icon,
  title,
  description,
}: {
  icon: string;
  title: string;
  description: string;
}) => (
  <div className="capabilities-alert-container">
    <EDSIcon icon={icon as Icon}></EDSIcon>
    <div>
      <div className="capabilities-status-alert__title">{title}</div>
      <RichText text={description} />
    </div>
  </div>
);

export const TitleLoaderSection = () => (
  <>
    <div className='loader-wrapper'>
        <SkeletonLoader list={[{ rows: Array(1).fill({ className: 'card-user-details__loading' }) }]} />
    </div>
     <SkeletonLoader list={cardsLoaderList} />
    </>
);