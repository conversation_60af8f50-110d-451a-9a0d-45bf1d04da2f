import { BreadcrumbProps } from './models';
import { BreadcrumbComponent, GridRow, GridCell, GridContainer } from 'cx-dle-component-library';
import './styles.scss';
import { OMNITURE_EVENTS } from 'cx-dle-common-lib';
import { breadCrumb } from '../../constants';


const BreadcrumbControl: React.FC<BreadcrumbProps> = (props) => {
  const { breadcrumbLink, breadcrumbNavigationTitle, breadcrumbPageTitle } = props;

  if (!!breadcrumbLink && !!breadcrumbNavigationTitle) {
    return (
      <div className={ "ge-breadcrumb-nav-wrapper"}>
        <GridContainer className={"ge-breadcrumb-nav-container"}>
          <GridRow>
            <GridCell desktop={12} tablet={4} phone={4}>
              <BreadcrumbComponent
                navigationLink={breadcrumbLink}
                navigationTitle={breadcrumbNavigationTitle}
                pageTitle={breadcrumbPageTitle}
                data-analytics-tracking-event={OMNITURE_EVENTS.LINK_CLICK}
                data-analytics-link-name={breadcrumbNavigationTitle}
                data-analytics-link-type={`${breadCrumb?.BREAD_CRUMB_NAME}`}
                data-analytics-link-title={`${breadcrumbPageTitle}`}
              />
            </GridCell>
          </GridRow>
        </GridContainer>
      </div>
    );
  }
  return null;
};

export default BreadcrumbControl;