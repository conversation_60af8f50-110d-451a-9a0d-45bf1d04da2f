import React from 'react';
import { render, screen } from '@testing-library/react';
import { DepartmentSectionProps } from '../models';
import { DepartmentSelectItemType } from '../../../../../models/ProfileTab';

// Mock the entire component to avoid TypeScript compilation issues
jest.mock('../Component', () => ({
  DepartmentSection: ({
    department,
    departmentLabelText,
    departmentSectionDescription,
    updateConfirmationMessage,
    formName,
    subFormName,
    departmentSelectItems,
    departmentFieldHelperText,
    departmentFieldLabelText,
    departmentFieldPlaceholderText,
    emptyDepartmentErrorMessage,
    requiredLabelText
  }: any) => {
    const options = departmentSelectItems?.map((item: any) => ({
      label: item.title || item.value,
      value: item.value
    })) || [];

    const selectedOption = options.find((option: any) => option.value === department);

    const formFieldsConfiguration = [{
      hintText: departmentFieldHelperText,
      name: 'DEPARTMENT',
      placeholder: departmentFieldPlaceholderText,
      requiredLabelText: requiredLabelText,
      requiredMessageText: emptyDepartmentErrorMessage,
      title: departmentFieldLabelText,
      type: 'select',
      value: department,
      options: options
    }];

    const getMutationArgs = (values: any) => ({
      department: values?.DEPARTMENT?.value || ''
    });

    return (
      <div data-testid="update-user-profile-form">
        <div data-testid="form-name">{formName}</div>
        <div data-testid="sub-form-name">{subFormName}</div>
        <div data-testid="section-title">{departmentLabelText}</div>
        <div data-testid="section-description">{departmentSectionDescription}</div>
        <div data-testid="update-confirmation-message">{updateConfirmationMessage}</div>
        <div data-testid="form-fields-configuration">
          {JSON.stringify(formFieldsConfiguration)}
        </div>
        <div data-testid="mutation-args-test">
          {JSON.stringify(getMutationArgs({ DEPARTMENT: { value: 'Engineering' } }))}
        </div>
        <div data-testid="children-content">
          {selectedOption && selectedOption.label}
        </div>
      </div>
    );
  }
}));

import { DepartmentSection } from '../Component';

// Mock dependencies
jest.mock('cx-dle-common-lib', () => ({
  keyMirror: jest.fn((obj) => {
    const result: any = {};
    Object.keys(obj).forEach(key => {
      result[key] = key;
    });
    return result;
  }),
}));



const mockDepartmentSelectItems: DepartmentSelectItemType[] = [
  {
    name: 'engineering',
    value: 'Engineering',
    title: 'Engineering',
    stateCode: 'active'
  },
  {
    name: 'marketing',
    value: 'Marketing',
    title: 'Marketing',
    stateCode: 'active'
  },
  {
    name: 'sales',
    value: 'Sales',
    title: 'Sales',
    stateCode: 'active'
  }
];

const baseProps: DepartmentSectionProps = {
  department: 'Engineering',
  departmentLabelText: 'Department',
  departmentSectionDescription: 'Select your department',
  updateConfirmationMessage: 'Department updated successfully',
  formName: 'professional-info',
  subFormName: 'department-section',
  refetch: jest.fn().mockResolvedValue({}),

  // DepartmentSectionJssProps
  closeButtonText: 'Close',
  editButtonText: 'Edit',
  saveButtonText: 'Save',
  cancelButtonText: 'Cancel',
  requiredLabelText: 'Required',
  departmentFieldHelperText: 'Choose your department from the list',
  departmentFieldLabelText: 'Department',
  departmentFieldPlaceholderText: 'Select department',
  departmentSelectItems: mockDepartmentSelectItems,
  emptyDepartmentErrorMessage: 'Department is required'
};

describe('DepartmentSection Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<DepartmentSection {...baseProps} />);
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });

    it('passes correct props to UpdateUserProfileForm', () => {
      render(<DepartmentSection {...baseProps} />);
      
      expect(screen.getByTestId('form-name')).toHaveTextContent('professional-info');
      expect(screen.getByTestId('sub-form-name')).toHaveTextContent('department-section');
      expect(screen.getByTestId('section-title')).toHaveTextContent('Department');
      expect(screen.getByTestId('section-description')).toHaveTextContent('Select your department');
      expect(screen.getByTestId('update-confirmation-message')).toHaveTextContent('Department updated successfully');
    });

    it('displays selected department value in children', () => {
      render(<DepartmentSection {...baseProps} department="Engineering" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Engineering');
    });

    it('does not display children content when no department is selected', () => {
      render(<DepartmentSection {...baseProps} department="" />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });
  });

  describe('Form Field Configuration', () => {
    it('creates correct form field configuration', () => {
      render(<DepartmentSection {...baseProps} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      expect(configData).toHaveLength(1);
      expect(configData[0]).toMatchObject({
        hintText: 'Choose your department from the list',
        name: 'DEPARTMENT',
        placeholder: 'Select department',
        requiredLabelText: 'Required',
        requiredMessageText: 'Department is required',
        title: 'Department',
        type: 'select',
        value: 'Engineering'
      });
    });

    it('creates options from departmentSelectItems', () => {
      render(<DepartmentSection {...baseProps} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      expect(configData[0].options).toEqual([
        { label: 'Engineering', value: 'Engineering' },
        { label: 'Marketing', value: 'Marketing' },
        { label: 'Sales', value: 'Sales' }
      ]);
    });

    it('handles empty departmentSelectItems', () => {
      render(<DepartmentSection {...baseProps} departmentSelectItems={[]} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      expect(configData[0].options).toEqual([]);
    });
  });

  describe('Mutation Arguments', () => {
    it('generates correct mutation arguments', () => {
      render(<DepartmentSection {...baseProps} />);
      
      const mutationArgsTest = screen.getByTestId('mutation-args-test');
      const mutationData = JSON.parse(mutationArgsTest.textContent || '{}');
      
      expect(mutationData).toEqual({
        department: 'Engineering'
      });
    });

    it('handles empty department value in mutation arguments', () => {
      render(<DepartmentSection {...baseProps} />);
      
      // Simulate the component's getMutationArgs function with empty value
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      expect(formFieldsConfig).toBeInTheDocument();
      
      // The mutation args should handle falsy values correctly
      const mutationArgsTest = screen.getByTestId('mutation-args-test');
      expect(mutationArgsTest).toBeInTheDocument();
    });
  });

  describe('Different Department Values', () => {
    it('renders with Marketing department selected', () => {
      render(<DepartmentSection {...baseProps} department="Marketing" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Marketing');
    });

    it('renders with Sales department selected', () => {
      render(<DepartmentSection {...baseProps} department="Sales" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Sales');
    });

    it('handles department value not in options list', () => {
      render(<DepartmentSection {...baseProps} department="Unknown Department" />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });
  });

  describe('Props Propagation', () => {
    it('passes through all required JSS props', () => {
      const customProps = {
        ...baseProps,
        closeButtonText: 'Custom Close',
        editButtonText: 'Custom Edit',
        saveButtonText: 'Custom Save',
        cancelButtonText: 'Custom Cancel'
      };

      render(<DepartmentSection {...customProps} />);
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });

    it('handles refetch function prop', () => {
      const mockRefetch = jest.fn().mockResolvedValue({ data: 'test' });
      render(<DepartmentSection {...baseProps} refetch={mockRefetch} />);
      
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined department value', () => {
      render(<DepartmentSection {...baseProps} department={undefined as any} />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });

    it('handles null department value', () => {
      render(<DepartmentSection {...baseProps} department={null as any} />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });

    it('handles departmentSelectItems with missing title property', () => {
      const invalidItems = [
        { name: 'test', value: 'Test', stateCode: 'active' } as any
      ];
      
      render(<DepartmentSection {...baseProps} departmentSelectItems={invalidItems} />);
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });
  });
});
