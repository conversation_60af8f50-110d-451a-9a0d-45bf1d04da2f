import React, { createContext, useCallback, useContext, useState } from 'react';
import { NotificationsPreferencesContextType } from '../types/common';
import { NotificationType } from '../components/ServiceNotificationPreferences/models';
import { getLocalizedSFDCNotificationFlag } from '../components/ServiceNotificationPreferences/utils';
import { SfdcNotificationsFlagsLocalizationType } from '../models/Sitecommoncontent';

// Create the context with an undefined default value.
const NotificationsPreferencesContext = createContext<NotificationsPreferencesContextType | undefined>(undefined);

export const useNotificationsPreferences = () => {
  const context = useContext(NotificationsPreferencesContext);
  if (!context) {
    throw new Error('useNotificationsPreferences must be used within a NotificationsPreferencesProvider');
  }
  return context;
};

export const NotificationsPreferencesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [preferences, setPreferences] = useState<NotificationType[]>([]); // Default preferences
  const [selected, setSelected] = useState<string[]>([]);
  const [isSaved, setSaved] = useState<boolean>(false);

  const updateNotificationPreferences = (newPreferences: NotificationType[]) => {
    setPreferences(newPreferences);
  };

  const getAllSelectedPreferences = useCallback(
    (
      notificationTypeList: NotificationType[],
      channelName: string,
      sfdcNotificationsFlagsLocalization: SfdcNotificationsFlagsLocalizationType,
    ) => {
      return notificationTypeList
        .filter(
          (nSelect) =>
            nSelect?.preferred && nSelect?.notificationChannel.toLowerCase().includes(channelName.toLowerCase()),
        )
        .map((select) => {
          const nCode = getLocalizedSFDCNotificationFlag(sfdcNotificationsFlagsLocalization, select.notificationCode);
          return nCode?.title ?? null;
        });
    },
    [],
  );

  const setSelectedPreferences = (
    notificationTypeList: NotificationType[],
    channelName: string,
    sfdcNotificationsFlagsLocalization: SfdcNotificationsFlagsLocalizationType,
  ) => {
    const selected = getAllSelectedPreferences(notificationTypeList, channelName, sfdcNotificationsFlagsLocalization);

    setSelected(selected);
  };

  const setDataSaved = (dataSaved: boolean) => {
    setSaved(dataSaved);
  };

  return (
    <NotificationsPreferencesContext.Provider
      value={{
        preferences,
        updateNotificationPreferences,
        setSelectedPreferences,
        selectedPreferences: selected,
        setDataSaved,
        isDataSaved: isSaved,
      }}
    >
      {children}
    </NotificationsPreferencesContext.Provider>
  );
};
