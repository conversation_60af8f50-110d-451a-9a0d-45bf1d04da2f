import { AlertMessage, RichText } from 'cx-dle-component-library';
import { formattedString } from './common';
import { InformationCardProps } from './models';
import './styles.scss';
import { PHONE } from '../../../constants';

export const InformationCard = ({
  title,
  statusInfo,
  alert,
  updateConfirmationMessage,
  onAlertCloseClick,
  children,
  iserror,
  errormessage,
  id,
}: InformationCardProps) => {
  return (
    <>
      {iserror && id != PHONE ? (
        <AlertMessage className="information-card-alert" type={'danger'} onCloseClick={onAlertCloseClick}>
          <RichText text={formattedString(errormessage?.text, title)} className="information-card-alert__text" />
        </AlertMessage>
      ) : (
        id != PHONE &&
        alert?.text && (
          <AlertMessage className="information-card-alert" type={alert?.type} onCloseClick={onAlertCloseClick}>
            <RichText
              text={formattedString(updateConfirmationMessage, title)}
              className="information-card-alert__text"
            />
          </AlertMessage>
        )
      )}
      <div className="information-card">
        <div className="information-card__title">
          <RichText className="card_title" text={title} />
        </div>
        {id === PHONE ? (
          <div className="information-card__value">{children || '---'}</div>
        ) : (
          <div className="information-card__value">{children}</div>
        )}
        {!!statusInfo && (
          <div className="information-card__status-info">
            <RichText text={statusInfo} className="status_info" />
          </div>
        )}
      </div>
    </>
  );
};
