import { Text } from 'cx-dle-component-library';
import { NotificationCardProps } from './models';
import './styles.scss';

export const ServiceNotificationCard = (props: NotificationCardProps) =>  (
    <div style={{ width: '100%' }}>
      <div className="ge-user-preferences__info" aria-hidden="true">
        <Text tag="div" className="ge-user-preferences__info-title" text={props.infoTitle} />
      </div>
      {props.showDescriptionLabel ? (
        <Text tag="div" className="ge-user-preferences__info-names" text={props.descriptionLabel} />
      ) : (
        props.preferencesComponent
      )}
    </div>
  );
