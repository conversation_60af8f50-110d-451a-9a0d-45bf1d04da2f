import * as fs from 'fs';
import * as path from 'path';

// Array of CDN URLs

const CDN_URLS = [
  'https://dev.gehealthcare.com/accountcdn/content/en-us/account.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/myteam.json',
  'https://dev.gehealthcare.com/accountcdn/content/fr-fr/settings/paymentTab.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/settings.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/avuriDeviceRegistration.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/settings/profileTab.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/settings/securityTab.json',
  'https://qa.gehealthcare.com/accountcdn/content/en-us/settings/communicationsTab.json',
  'https://dev.gehealthcare.com/dlecdn/content/en-us/dlesitecommoncontent.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/notifications.json',
  'https://dev.gehealthcare.com/accountcdn/content/en-us/sitecommoncontent.json',
];

const outputDir = path.join(__dirname, '..', 'src', 'models');

interface PropertyInfo {
  name: string;
  type: string;
}

function capitalizeFirstLetter(string: string): string {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

function getPropertyType(value: any, key: string): string {
  if (Array.isArray(value)) {
    const itemType = value.length > 0 ? getPropertyType(value[0], key.slice(0, -1)) : 'any';
    return `${itemType}[]`;
  } else if (typeof value === 'object' && value !== null) {
    return capitalizeFirstLetter(key) + 'Type';
  } else if (typeof value === 'string') {
    if (/^(https?:\/\/)/.test(value)) {
      return 'URL';
    } else if (/^\d{4}-\d{2}-\d{2}/.test(value)) {
      return 'Date';
    } else {
      return 'string';
    }
  } else {
    return typeof value;
  }
}

function generateInterface(name: string, obj: any): string {
  const properties: PropertyInfo[] = Object.entries(obj).map(([key, value]) => ({
    name: key,
    type: getPropertyType(value, key),
  }));

  const interfaceContent = properties.map((prop) => `  ${prop.name}: ${prop.type};`).join('\n');

  return `export interface ${name} {\n${interfaceContent}\n}`;
}

function generateModels(data: any, rootName: string): string[] {
  const interfaces: string[] = [];

  function generateModelRecursive(obj: any, parentKey: string = ''): void {
    if (typeof obj === 'object' && obj !== null) {
      if (Array.isArray(obj)) {
        if (obj.length > 0 && typeof obj[0] === 'object') {
          generateModelRecursive(obj[0], parentKey.slice(0, -1));
        }
      } else {
        const interfaceName = parentKey ? capitalizeFirstLetter(parentKey) + 'Type' : rootName;
        interfaces.push(generateInterface(interfaceName, obj));

        for (const [key, value] of Object.entries(obj)) {
          if (typeof value === 'object' && value !== null) {
            generateModelRecursive(value, key);
          }
        }
      }
    }
  }

  generateModelRecursive(data);
  return interfaces;
}

async function fetchJSONFromCDN(url: string): Promise<any> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Failed to fetch JSON from CDN (${url}):`, error);
    throw error;
  }
}

function getModelNameFromURL(url: string): string {
  const fileName = path.basename(new URL(url).pathname, '.json');
  return capitalizeFirstLetter(fileName);
}

async function generateModelForURL(url: string): Promise<void> {
  try {
    const jsonData = await fetchJSONFromCDN(url);
    const modelName = getModelNameFromURL(url);
    const generatedInterfaces = generateModels(jsonData, modelName);
    const content = `// This file is auto-generated. Do not edit manually.\n\n${generatedInterfaces.join('\n\n')}`;

    const outputPath = path.join(outputDir, `${modelName}.ts`);
    fs.writeFileSync(outputPath, content);
    console.log(`Models generated successfully for ${modelName}!`);
  } catch (error) {
    console.error(`Error generating models for ${url}:`, error);
  }
}

async function main() {
  try {
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    for (const url of CDN_URLS) {
      await generateModelForURL(url);
    }
  } catch (error) {
    console.error('Error in main function:', error);
    process.exit(1);
  }
}

main();
export { main };
