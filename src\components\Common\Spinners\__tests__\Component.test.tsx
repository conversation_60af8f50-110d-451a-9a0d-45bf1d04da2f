// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { GeSpinner } from '../Component';
import { SpinnerProps } from '../models';

// Mock classNames
jest.mock('classnames', () => {
  return jest.fn((...args) => {
    return args
      .filter(Boolean)
      .map(arg => typeof arg === 'string' ? arg : Object.keys(arg).filter(key => arg[key]).join(' '))
      .join(' ');
  });
});

// Mock SCSS imports
jest.mock('../styles.scss', () => ({}));

describe('GeSpinner Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      const { container } = render(<GeSpinner />);
      expect(container.firstChild).toBeInTheDocument();
    });

    it('renders as a div element', () => {
      const { container } = render(<GeSpinner />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv?.nodeName.toLowerCase()).toBe('div');
    });

    it('applies default ge-spinner class', () => {
      const { container } = render(<GeSpinner />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
    });

    it('renders exactly three span elements', () => {
      const { container } = render(<GeSpinner />);
      const spans = container.querySelectorAll('span');
      
      expect(spans).toHaveLength(3);
    });

    it('all span elements are direct children of the spinner div', () => {
      const { container } = render(<GeSpinner />);
      const spinnerDiv = container.firstChild;
      const directSpanChildren = Array.from(spinnerDiv?.children || []).filter(
        child => child.nodeName.toLowerCase() === 'span'
      );
      
      expect(directSpanChildren).toHaveLength(3);
    });
  });

  describe('Props Handling', () => {
    it('applies custom className when provided', () => {
      const props: SpinnerProps = {
        className: 'custom-spinner-class'
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
      expect(spinnerDiv).toHaveClass('custom-spinner-class');
    });

    it('handles multiple custom classes', () => {
      const props: SpinnerProps = {
        className: 'class-one class-two'
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
      expect(spinnerDiv).toHaveClass('class-one');
      expect(spinnerDiv).toHaveClass('class-two');
    });

    it('handles undefined className gracefully', () => {
      const props: SpinnerProps = {
        className: undefined
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
    });

    it('handles empty string className', () => {
      const props: SpinnerProps = {
        className: ''
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
    });

    it('handles null className', () => {
      const props: SpinnerProps = {
        className: null as any
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
    });
  });

  describe('Structure and Accessibility', () => {
    it('maintains consistent DOM structure', () => {
      const { container } = render(<GeSpinner />);
      
      // Check main container
      const spinnerDiv = container.firstChild;
      expect(spinnerDiv?.nodeName.toLowerCase()).toBe('div');
      
      // Check span children
      const spans = container.querySelectorAll('span');
      spans.forEach(span => {
        expect(span.nodeName.toLowerCase()).toBe('span');
        expect(span.textContent).toBe(''); // Spans should be empty
      });
    });

    it('spans have no text content (used for animation)', () => {
      const { container } = render(<GeSpinner />);
      const spans = container.querySelectorAll('span');
      
      spans.forEach(span => {
        expect(span.textContent).toBe('');
        expect(span.innerHTML).toBe('');
      });
    });

    it('does not have any accessibility attributes by default', () => {
      const { container } = render(<GeSpinner />);
      const spinnerDiv = container.firstChild;
      
      // Component doesn't set aria-label, role, etc. by default
      expect(spinnerDiv).not.toHaveAttribute('role');
      expect(spinnerDiv).not.toHaveAttribute('aria-label');
      expect(spinnerDiv).not.toHaveAttribute('aria-live');
    });
  });

  describe('CSS Classes Integration', () => {
    it('calls classNames utility with correct parameters', () => {
      const classNames = require('classnames');
      const props: SpinnerProps = {
        className: 'test-class'
      };
      
      render(<GeSpinner {...props} />);
      
      expect(classNames).toHaveBeenCalledWith('ge-spinner', 'test-class');
    });

    it('calls classNames utility with only default class when no custom class provided', () => {
      const classNames = require('classnames');
      
      render(<GeSpinner />);
      
      expect(classNames).toHaveBeenCalledWith('ge-spinner', undefined);
    });
  });

  describe('Component Variants', () => {
    it('renders consistently with different className combinations', () => {
      const testCases = [
        { className: 'small' },
        { className: 'large' },
        { className: 'primary' },
        { className: 'secondary' },
        { className: 'small primary' },
        { className: 'large secondary' }
      ];

      testCases.forEach(({ className }) => {
        const { container, unmount } = render(<GeSpinner className={className} />);
        
        const spinnerDiv = container.firstChild;
        expect(spinnerDiv).toHaveClass('ge-spinner');
        expect(spinnerDiv).toHaveClass(className.split(' ')[0]);
        
        const spans = container.querySelectorAll('span');
        expect(spans).toHaveLength(3);
        
        unmount();
      });
    });
  });

  describe('Performance and Re-rendering', () => {
    it('maintains same structure on re-render with same props', () => {
      const { container, rerender } = render(<GeSpinner className="test" />);
      
      const initialSpinnerDiv = container.firstChild;
      const initialSpans = container.querySelectorAll('span');
      
      rerender(<GeSpinner className="test" />);
      
      const rerenderedSpinnerDiv = container.firstChild;
      const rerenderedSpans = container.querySelectorAll('span');
      
      expect(rerenderedSpinnerDiv).toHaveClass('ge-spinner');
      expect(rerenderedSpinnerDiv).toHaveClass('test');
      expect(rerenderedSpans).toHaveLength(3);
    });

    it('updates className on prop change', () => {
      const { container, rerender } = render(<GeSpinner className="initial" />);
      
      let spinnerDiv = container.firstChild;
      expect(spinnerDiv).toHaveClass('initial');
      
      rerender(<GeSpinner className="updated" />);
      
      spinnerDiv = container.firstChild;
      expect(spinnerDiv).toHaveClass('updated');
      expect(spinnerDiv).not.toHaveClass('initial');
    });
  });

  describe('Edge Cases', () => {
    it('handles special characters in className', () => {
      const props: SpinnerProps = {
        className: 'test-class_with-special.chars'
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
      expect(spinnerDiv).toHaveClass('test-class_with-special.chars');
    });

    it('handles very long className', () => {
      const longClassName = 'a'.repeat(1000);
      const props: SpinnerProps = {
        className: longClassName
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
      expect(spinnerDiv).toHaveClass(longClassName);
    });

    it('handles className with leading/trailing spaces', () => {
      const props: SpinnerProps = {
        className: '  test-class  '
      };
      
      const { container } = render(<GeSpinner {...props} />);
      const spinnerDiv = container.firstChild;
      
      expect(spinnerDiv).toHaveClass('ge-spinner');
      // Note: The exact behavior depends on how classNames handles whitespace
      expect(spinnerDiv.className).toContain('test-class');
    });
  });

  describe('Integration', () => {
    it('can be used within other components', () => {
      const WrapperComponent = ({ loading }: { loading: boolean }) => (
        <div data-testid="wrapper">
          {loading && <GeSpinner className="loading-spinner" />}
          <div>Content</div>
        </div>
      );
      
      const { container } = render(<WrapperComponent loading={true} />);
      
      expect(screen.getByTestId('wrapper')).toBeInTheDocument();
      expect(container.querySelector('.ge-spinner')).toBeInTheDocument();
      expect(container.querySelector('.loading-spinner')).toBeInTheDocument();
    });

    it('renders multiple spinners independently', () => {
      const { container } = render(
        <div>
          <GeSpinner className="spinner-1" />
          <GeSpinner className="spinner-2" />
          <GeSpinner className="spinner-3" />
        </div>
      );
      
      const spinners = container.querySelectorAll('.ge-spinner');
      expect(spinners).toHaveLength(3);
      
      expect(container.querySelector('.spinner-1')).toBeInTheDocument();
      expect(container.querySelector('.spinner-2')).toBeInTheDocument();
      expect(container.querySelector('.spinner-3')).toBeInTheDocument();
      
      // Each spinner should have 3 spans
      const allSpans = container.querySelectorAll('span');
      expect(allSpans).toHaveLength(9); // 3 spinners × 3 spans each
    });
  });
});
