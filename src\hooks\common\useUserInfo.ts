import { gql, useQuery } from '@apollo/client';
import { isEUSite } from '../../utils';
import { useState } from 'react';

export interface AssignedApplication {
  displayName: string;
  applicationName: string;
}

export interface ProgressiveRegistration {
  umrStatus: string;
  applicationName: string;
}

export interface Onboarded {
  mobile: boolean;
  desktop: boolean;
}

export interface UserDetails {
  assignedApplications: AssignedApplication[];
  progressiveRegistrations: ProgressiveRegistration[];
  userName: string;
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  region: string;
  country: string;
  role: string;
  language: string;
  isHoldingAccount: boolean;
  accountName: string;
  department: string;
  contactPhone: string;
  extension?: string;
  onboarded: Onboarded;
  facilityType: string;
  termsAndConditions: boolean;
  userType: string;
  custAdmin: boolean;
}

export interface UserInfo {
  userAccountDetails: UserDetails;
}

const userDetailsQuery = gql`
  query userAccountDetails {
    userAccountDetails {
      assignedApplications {
        displayName
        applicationName
        __typename
      }
      progressiveRegistrations {
        umrStatus
        applicationName
        __typename
      }
      userName
      userId
      email
      firstName
      lastName
      region
      country
      role
      language
      isHoldingAccount
      contactPhone
      extension
      accountName
      department
      facilityType
      termsAndConditions
      __typename
      userType
      custAdmin
    }
  }
`;

const userDetailsQueryEMEA = gql`
  query userAccountDetails {
    userAccountDetails {
      assignedApplications {
        displayName
        applicationName
        __typename
      }
      progressiveRegistrations {
        umrStatus
        applicationName
        __typename
      }
      userName
      userId
      email
      firstName
      lastName
      region
      country
      role
      language
      isHoldingAccount
      contactPhone
      extension
      accountName
      department
      facilityType
      termsAndConditions
      onboarded {
        mobile
        desktop
      }
      __typename
    }
  }
`;

export const userProfessionalInfoQuery = gql`query userProfessionalInfo{
  userProfessionalInfo {
      contactDetail {
        localFirstName
        localLastName
      }
  }
}`;

export const useUserInfo = () => {
  const [isError, setError] = useState(true);
  const isSiteEMEA = isEUSite();
  const { data, loading, error, refetch } = useQuery<UserInfo>(isSiteEMEA ? userDetailsQueryEMEA : userDetailsQuery);
  return { data, loading, error: error && isError, setError, refetch };
};
