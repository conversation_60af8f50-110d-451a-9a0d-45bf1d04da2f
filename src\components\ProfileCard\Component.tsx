import { <PERSON><PERSON><PERSON>ontainer } from 'cx-dle-component-library';
import { ProfileCardProps } from './model';
import './styles.scss';
import { CardInfo } from './CardInfo/Component';
import {
  checkApplicationAccess,
  getApplicationNamesForLinksByCountry,
  shouldShowComponent,
} from '../../utils/commonUtils';
import { userAccountDetails } from '../../hooks/common/useAccountDetails';
import { useLazyQuery } from '@apollo/client';
import { useEffect, useMemo } from 'react';
import { notificationQuery } from './query';

export const ProfileCard = (props: ProfileCardProps) => {
  const { userInfo, profileCard, supportedCountries, userInfoLoading } = props;
  const { hasEquipmentAccess, hasOrdersAccess, hasTrainingAccess, profileInfo, organization } =
    userAccountDetails(userInfo);

  const [getNotificationsCount, { loading: notificationsLoading, data: notificationsData }] =
    useLazyQuery(notificationQuery);

  const notificationCount = useMemo(() => notificationsData?.collection?.totalRows, [notificationsData]);

  useEffect(() => {
    if (hasEquipmentAccess) {
      getNotificationsCount({
        variables: { queryContext: { filter: { read: false } } },
      });
    }
  }, [userInfo, hasEquipmentAccess]);

  const userCountry = userInfo?.userAccountDetails?.country;
  const displayProfileSection = shouldShowComponent(supportedCountries, profileInfo?.country);
  const applicationsToShowLink = getApplicationNamesForLinksByCountry(
    profileCard?.communicationsPreferencesLink,
    userCountry as string,
  );
  const hasAssignedApplications = checkApplicationAccess(
    userInfo?.userAccountDetails?.assignedApplications,
    applicationsToShowLink,
  );
  const displayCommPreferencesLink =
    shouldShowComponent(profileCard?.communicationsPreferencesLink?.supportedCountries, userCountry as string) &&
    hasAssignedApplications;

  return (
    <>
      <div className="card-wrapper__parent">
        {displayProfileSection && (
          <GridContainer className="card-wrapper">
            <div className="card">
              <CardInfo
                organization={organization}
                cardType={'profile'}
                profileInfo={profileInfo}
                props={props}
                showSkeletonLoader={userInfoLoading}
                showCommunicationPreferences={
                  (hasEquipmentAccess || hasOrdersAccess || hasTrainingAccess) && displayCommPreferencesLink
                }
              />
              <CardInfo
                organization={organization}
                cardType={'help'}
                profileInfo={profileInfo}
                props={props}
                showSkeletonLoader={userInfoLoading}
              />
              <CardInfo
                organization={organization}
                cardType={'notification'}
                profileInfo={profileInfo}
                props={props}
                hasNotification={notificationCount > 0}
                notificationCount={notificationCount}
                showNotificationCard={hasEquipmentAccess}
                showSkeletonLoader={userInfoLoading || notificationsLoading}
              />
            </div>
          </GridContainer>
        )}
      </div>
    </>
  );
};
