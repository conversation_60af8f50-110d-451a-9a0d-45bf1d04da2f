import { FORM_FIELDS } from './constants';
import { NameSectionProps } from './models';
import { InformationFormField } from '../LanguageSection/models';
import { FormValues } from 'cx-dle-common-lib';
import UpdateUserProfileFormControl from '../../../Common/UpdateUserProfileForm';

export const NameSection = ({
  firstName,
  localFirstName,
  lastName,
  localLastName,
  nameLabelText,
  nameSectionDescription,
  updateConfirmationMessage,
  formName,
  showLocalNameFields,
  subFormName,
  ...other
}: NameSectionProps) => {
  let formFieldsConfiguration: InformationFormField[];
  if (showLocalNameFields === true) {
    formFieldsConfiguration = [
      {
        hintText: other.lastNameHelperText,
        name: FORM_FIELDS.LAST_NAME,
        placeholder: other.lastNamePlaceholderText,
        requiredLabelText: other.requiredLabelText,
        requiredMessageText: other.emptyLastNameErrorMessage,
        title: other.lastNameLabelText,
        type: 'text',
        value: lastName,
      },
      {
        hintText: other.firstNameHelperText,
        name: FORM_FIELDS.FIRST_NAME,
        placeholder: other.firstNamePlaceholderText,
        requiredLabelText: other.requiredLabelText,
        requiredMessageText: other.emptyFirstNameErrorMessage,
        title: other.firstNameLabelText,
        type: 'text',
        value: firstName,
      },
      {
        hintText: other.lastLocalNameHelperText,
        name: FORM_FIELDS.LOCAL_LAST_NAME,
        placeholder: other.lastLocalNamePlaceholderText,
        requiredLabelText: other.requiredLabelText,
        requiredMessageText: other.emptyLastLocalNameErrorMessage,
        title: other.lastLocalNameLabelText,
        type: 'text',
        value: localLastName,
      },
      {
        hintText: other.firstLocalNameHelperText,
        name: FORM_FIELDS.LOCAL_FIRST_NAME,
        placeholder: other.firstLocalNamePlaceholderText,
        requiredLabelText: other.requiredLabelText,
        requiredMessageText: other.emptyFirstLocalNameErrorMessage,
        title: other.firstLocalNameLabelText,
        type: 'text',
        value: localFirstName,
      },
    ];
  } else {
    formFieldsConfiguration = [
      {
        hintText: other.firstNameHelperText,
        name: FORM_FIELDS.FIRST_NAME,
        placeholder: other.firstNamePlaceholderText,
        requiredLabelText: other.requiredLabelText,
        requiredMessageText: other.emptyFirstNameErrorMessage,
        title: other.firstNameLabelText,
        type: 'text',
        value: firstName,
      },
      {
        hintText: other.lastNameHelperText,
        name: FORM_FIELDS.LAST_NAME,
        placeholder: other.lastNamePlaceholderText,
        requiredLabelText: other.requiredLabelText,
        requiredMessageText: other.emptyLastNameErrorMessage,
        title: other.lastNameLabelText,
        type: 'text',
        value: lastName,
      },
    ];
  }
  const getMutationArgs = (values: FormValues): any => ({
    firstName: values[FORM_FIELDS.FIRST_NAME],
    lastName: values[FORM_FIELDS.LAST_NAME],
    localFirstName: showLocalNameFields === true ? values[FORM_FIELDS.LOCAL_FIRST_NAME] : '',
    localLastName: showLocalNameFields === true ? values[FORM_FIELDS.LOCAL_LAST_NAME] : '',
  });

  return (
    <UpdateUserProfileFormControl
      formName={formName}
      subFormName={subFormName}
      sectionTitle={nameLabelText}
      sectionDescription={nameSectionDescription}
      updateConfirmationMessage={updateConfirmationMessage}
      formFieldsConfiguration={formFieldsConfiguration as unknown as []}
      getMutationArgs={getMutationArgs}
      {...other}
      refetch={async () => {
        await other.refetch();
      }}
    >
      {showLocalNameFields === true ? `${lastName} ${firstName}` : ` ${firstName} ${lastName}`}
    </UpdateUserProfileFormControl>
  );
};
