import React from 'react';
import { SkeletonLoaderProps, SkeletonStyles } from './models';
import './styles.scss';

export const SkeletonLoader = ({ list }: SkeletonLoaderProps) => {
  const load = (list: SkeletonStyles[]) => {
    return list.map(({ width, height, margin, rows, columns, className }) => {
        return (
          <div>
            {rows instanceof Array &&
              rows.map((row) => (
                <div
                  style={{
                    width: row.width,
                    height: row.height,
                    display: 'flex',
                    flexDirection: (row.rows as SkeletonStyles[])?.length ? 'column' : 'row',
                    margin: row.margin,
                  }}
                  className={row.className}
                >
                  {load(row.columns as SkeletonStyles[] || row.rows || [row])}
                </div>
              ))}
            {columns instanceof Array && (
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                {columns.map((column) => (
                  <div
                    className={`skeleton-wrapper ${column.className || ''}`}
                    style={{ width: column.width, height: column.height, margin: column.margin }}
                  >
                    {load(column.rows  as SkeletonStyles[] || column.columns || [column])}
                  </div>
                ))}
              </div>
            )}

            {(width || height || className) && (
              <div className={`skeleton-wrapper ${className || ''}`} style={{ width, height, margin }} />
            )}
          </div>
        );
    });
  };
  return list?.length ? <React.Fragment>{load(list)}</React.Fragment> : null;
};
