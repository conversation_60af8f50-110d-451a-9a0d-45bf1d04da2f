export interface LanguageSectionJssProps {
  closeButtonText: string;
  editButtonText: string;
  saveButtonText: string;
  cancelButtonText: string;
  requiredLabelText: string;
  updateConfirmationMessage: string;
  languageTitle: string;
  languageDescription: string;
  newLanguageText: string;
  newLanguages: any;
}

export interface LanguageSectionProps extends LanguageSectionJssProps {
  language?: string;

  refetch: () => void;

  formName: string;
  subFormName: string;
}

export interface LanguageList {
  fields: {
    name: string;
    title: string;
    value: string;
  };
}

export type InformationFormFieldType = 'text' | 'select' | 'email';

export interface InformationFormField {
  title: string;
  type?: InformationFormFieldType;
  name: string;
  value: string;
  options?: [];
  placeholder?: string;
  hintText?: string;
  requiredLabelText?: string;
  requiredMessageText?: string;
  validationMessageText?: string;
  disabled?: boolean;
  onChangeSelect?: () => {};
  validateFunc?: () => {};
  validationExtension?: ValidationProps;
}

export interface ValidationProps {
  maxCharacters: number;
  regex: RegExp;
  supportedCountries: string[];
}
