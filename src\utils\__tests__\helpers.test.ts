import { Cookies } from 'react-cookie';
import moment from 'moment';
import {
  getReturnUrl,
  getAuthRedirectUrl,
  getSFDCLogInUrl,
  getSFDCLogOutUrl,
  isCSMMode,
  CSMRole,
  isCSMReadOnly,
  isCSMAdminMode,
  convertToFullBaseUrl,
  isEUSite,
  isLASite,
  isAuthenticated,
  getUserSpecificRedirectUrl,
  getUserSpecificLink,
  getLocalizedModality,
  getIdentityService,
} from '../helpers';
import getUserDetailsFromCookies from '../helpers';
import { AUTHENTICATION, CSM_ADMIN_COOKIE_NAME, CSM_MICRO_APP_COOKIE, GENERAL_COOKIES, localGetJwtPayload, Utils } from 'cx-dle-common-lib';
import { CSM_ACCESS_COOKIE_NAME, CSM_ADMIN_LEVEL_READWRITE, CSM_READ_ONLY_ACCESS, CSM_USER_DATA_STORAGE_KEY, currentDomain } from '../../constants';

// Mock dependencies
jest.mock('react-cookie');
jest.mock('moment');
jest.mock('../localeMapper', () => ({
  getLocaleInfoFromHostName: jest.fn(),
}));
jest.mock('../../hooks', () => ({
  useRedirect: jest.fn(),
}));
jest.mock('cx-dle-common-lib', () => ({
  AUTHENTICATION: {
    idTokenCookie: 'id_token',
    accessTokenCookie: 'access_token',
    authTypeCookie: 'auth_type',
    tokenExpirationDelta: 300000,
  },
  CSM_ADMIN_COOKIE_NAME: 'csm_admin',
  CSM_MICRO_APP_COOKIE: 'csm_micro_app',
  GENERAL_COOKIES: {
    menuId: 'menu_id',
  },
  localGetJwtPayload: jest.fn(),
  Utils: {
    getCookie: jest.fn(),
  },
}));
jest.mock('../../config', () => ({
  sfdcLoginUrl: 'https://login.salesforce.com',
  sfdcLogoutUrl: 'https://logout.salesforce.com',
  errorPages: {
    error403: '/error/403',
  },
  locales: {
    us: { hostname: 'us.example.com', localeCode: 'en-us' },
    ca: { hostname: 'ca.example.com', localeCode: 'en-ca' },
    uk: { hostname: 'uk.example.com', localeCode: 'en-gb' },
  },
  useIdentityServiceUrl: 'true',
}));

const mockCookies = Cookies as jest.MockedClass<typeof Cookies>;
const mockMoment = moment as jest.MockedFunction<typeof moment>;
const { getLocaleInfoFromHostName } = require('../localeMapper');
const { useRedirect } = require('../../hooks');

// Mock window and localStorage
const mockWindow = {
  document: { cookie: 'test-cookie=value' },
  location: {
    protocol: 'https:',
    search: '?param=value',
  },
};

const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true,
});

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

describe('helpers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getReturnUrl', () => {
    it('constructs return URL with current domain and search params', () => {
      const result = getReturnUrl('/dashboard');
      expect(result).toBe(`${currentDomain}/dashboard?param=value`);
    });
  });

  describe('getAuthRedirectUrl', () => {
    it('returns original auth URL when returnUrl is invalid', () => {
      const authUrl = 'https://auth.example.com';
      const result = getAuthRedirectUrl(authUrl, 'invalid-url');
      expect(result).toBe(authUrl);
    });

    it('appends returnUrl to auth URL query params', () => {
      const authUrl = 'https://auth.example.com?existing=param';
      const returnUrl = '/dashboard';
      const result = getAuthRedirectUrl(authUrl, returnUrl);
      expect(result).toContain('returnUrl=');
      expect(result).toContain(encodeURIComponent(`${currentDomain}/dashboard?param=value`));
    });

    it('handles root returnUrl correctly', () => {
      const authUrl = 'https://auth.example.com';
      const returnUrl = '/';
      const result = getAuthRedirectUrl(authUrl, returnUrl);
      expect(result).toContain('returnUrl=%2F');
    });
  });

  describe('getSFDCLogInUrl', () => {
    it('returns config login URL when available', () => {
      const result = getSFDCLogInUrl();
      expect(result).toBe('https://login.salesforce.com');
    });

    it('returns default URL when config URL starts with #', () => {
      const config = require('../../config');
      config.sfdcLoginUrl = '#default';
      const result = getSFDCLogInUrl();
      expect(result).toBeDefined();
    });
  });

  describe('getSFDCLogOutUrl', () => {
    it('returns config logout URL when available and no CSM user', () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      const result = getSFDCLogOutUrl();
      expect(result).toBe('https://logout.salesforce.com');
    });

    it('returns default URL when CSM user details exist', () => {
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        user: { userID: 'test-user-id' }
      }));
      const result = getSFDCLogOutUrl();
      expect(result).toBeDefined();
    });
  });

  describe('isCSMMode', () => {
    it('returns true when CSM admin cookie is true', () => {
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('true'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = isCSMMode();
      expect(result).toBe(true);
    });

    it('returns false when CSM admin cookie is false', () => {
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('false'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = isCSMMode();
      expect(result).toBe(false);
    });

    it('returns false when window is undefined', () => {
      const originalWindow = global.window;
      delete (global as any).window;

      const result = isCSMMode();
      expect(result).toBe(false);

      global.window = originalWindow;
    });
  });

  describe('CSMRole', () => {
    it('returns CSM role from cookies', () => {
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('admin'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = CSMRole();
      expect(result).toBe('admin');
    });

    it('returns null when window is undefined', () => {
      const originalWindow = global.window;
      delete (global as any).window;

      const result = CSMRole();
      expect(result).toBeNull();

      global.window = originalWindow;
    });
  });

  describe('isCSMReadOnly', () => {
    it('returns true when in CSM mode with read-only access', () => {
      const mockCookieInstance = {
        get: jest.fn()
          .mockReturnValueOnce('true') // CSM_ADMIN_COOKIE_NAME
          .mockReturnValueOnce(CSM_READ_ONLY_ACCESS), // CSM_ACCESS_COOKIE_NAME
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = isCSMReadOnly();
      expect(result).toBe(true);
    });

    it('returns false when not in CSM mode', () => {
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('false'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = isCSMReadOnly();
      expect(result).toBe(false);
    });
  });

  describe('isCSMAdminMode', () => {
    it('returns true when in CSM mode with admin access', () => {
      const mockCookieInstance = {
        get: jest.fn()
          .mockReturnValueOnce('true') // CSM_ADMIN_COOKIE_NAME
          .mockReturnValueOnce(CSM_ADMIN_LEVEL_READWRITE), // CSM_ACCESS_COOKIE_NAME
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = isCSMAdminMode();
      expect(result).toBe(true);
    });

    it('returns false when not in admin mode', () => {
      const mockCookieInstance = {
        get: jest.fn()
          .mockReturnValueOnce('true') // CSM_ADMIN_COOKIE_NAME
          .mockReturnValueOnce('read-only'), // CSM_ACCESS_COOKIE_NAME
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = isCSMAdminMode();
      expect(result).toBe(false);
    });
  });

  describe('convertToFullBaseUrl', () => {
    it('returns URL as-is when it contains http', () => {
      const url = 'https://external.example.com/path';
      const result = convertToFullBaseUrl(url);
      expect(result).toBe(url);
    });

    it('prepends current domain to relative URL', () => {
      const url = '/relative/path';
      const result = convertToFullBaseUrl(url);
      expect(result).toBe(`${currentDomain}/relative/path`);
    });

    it('adds slash and prepends domain when URL does not start with slash', () => {
      const url = 'relative/path';
      const result = convertToFullBaseUrl(url);
      expect(result).toBe(`${currentDomain}/relative/path`);
    });
  });

  describe('isEUSite', () => {
    it('returns true when API gateway region is EU', () => {
      getLocaleInfoFromHostName.mockReturnValue({ apiGatewayRegion: 'EU' });
      const result = isEUSite();
      expect(result).toBe(true);
    });

    it('returns false when API gateway region is not EU', () => {
      getLocaleInfoFromHostName.mockReturnValue({ apiGatewayRegion: 'US' });
      const result = isEUSite();
      expect(result).toBe(false);
    });
  });

  describe('isLASite', () => {
    it('returns true when API gateway region is LA', () => {
      getLocaleInfoFromHostName.mockReturnValue({ apiGatewayRegion: 'LA' });
      const result = isLASite();
      expect(result).toBe(true);
    });

    it('returns false when API gateway region is not LA', () => {
      getLocaleInfoFromHostName.mockReturnValue({ apiGatewayRegion: 'US' });
      const result = isLASite();
      expect(result).toBe(false);
    });
  });

  describe('getUserSpecificRedirectUrl', () => {
    it('returns URL for matching locale', () => {
      const result = getUserSpecificRedirectUrl('en-ca');
      expect(result).toBe('https://ca.example.com');
    });

    it('returns default US URL for non-matching locale', () => {
      const result = getUserSpecificRedirectUrl('fr-fr');
      expect(result).toBe('https://us.example.com');
    });
  });

  describe('getUserSpecificLink', () => {
    const mockLinks = [
      {
        countryCode: 'US',
        href: '/us-link',
        url: '/us-url',
        redirectLocale: 'en-us',
      },
      {
        countryCode: 'CA',
        href: '/ca-link',
        url: '/ca-url',
        redirectLocale: 'en-ca',
      },
    ];

    it('returns user-specific link based on country', () => {
      const mockUser = { address: { country: 'CA' } };
      (localGetJwtPayload as jest.Mock).mockReturnValue(mockUser);
      mockLocalStorage.getItem.mockReturnValue(null);

      const result = getUserSpecificLink(mockLinks, 'US');
      expect(result.countryCode).toBe('CA');
    });

    it('returns link with full URL when href contains http', () => {
      const linksWithHttp = [
        {
          countryCode: 'US',
          href: 'https://external.com/link',
          url: 'https://external.com/url',
          redirectLocale: 'en-us',
        },
      ];

      const result = getUserSpecificLink(linksWithHttp, 'US');
      expect(result.href).toBe('https://external.com/link');
    });
  });

  describe('getLocalizedModality', () => {
    const mockTimelineData = {
      modalityLocalization: [
        { modalityId: '1', name: 'CT Scan' },
        { modalityId: '2', name: 'MRI' },
      ],
    };

    it('returns localized modality for matching ID', () => {
      const result = getLocalizedModality(mockTimelineData, '1');
      expect(result).toEqual({ modalityId: '1', name: 'CT Scan' });
    });

    it('returns empty string for non-matching ID', () => {
      const result = getLocalizedModality(mockTimelineData, '999');
      expect(result).toBe('');
    });
  });

  describe('getIdentityService', () => {
    it('returns true when config value is "true"', () => {
      const result = getIdentityService();
      expect(result).toBe(true);
    });

    it('returns false when config value is not "true"', () => {
      const config = require('../../config');
      config.useIdentityServiceUrl = 'false';
      const result = getIdentityService();
      expect(result).toBe(false);
    });
  });
});
