import { FORM_FIELDS } from './constants';
import { EmailSectionProps } from './models';
import { FormValues } from 'cx-dle-common-lib';
import { InformationFormField } from '../LanguageSection/models';
import React from 'react';
import UpdateUserProfileFormControl from '../../../Common/UpdateUserProfileForm';

export const EmailSection = ({
  emailLabelText,
  emailSectionDescription,
  email,
  requestEditButtonText,
  updateConfirmationMessage,
  formName,
  subFormName,
  ...other
}: EmailSectionProps) => {
  const [alert, setAlert] = React.useState('');
  const formFieldsConfiguration: InformationFormField[] = [
    {
      hintText: other.emailFieldHelperText,
      name: FORM_FIELDS.EMAIL,
      placeholder: other.emailFieldPlaceholderText,
      requiredLabelText: other.requiredLabelText,
      requiredMessageText: other.emptyEmailErrorMessage,
      title: other.emailFieldLabelText,
      type: 'email',
      validationMessageText: other.invalidEmailErrorMessage,
      value: email,
    },
  ];

  const getMutationArgs = (values: FormValues): any => ({
    emailToBeUpdated: values[FORM_FIELDS.EMAIL],
  });

  return (
    <UpdateUserProfileFormControl
      formName={formName}
      subFormName={subFormName}
      sectionTitle={emailLabelText}
      sectionDescription={emailSectionDescription}
      formFieldsConfiguration={formFieldsConfiguration as any}
      getMutationArgs={getMutationArgs}
      updateConfirmationMessage={updateConfirmationMessage}
      successAlertType={'primary'}
      {...other}
      saveButtonText={requestEditButtonText}
      alert={alert}
      setAlert={() => setAlert}
      refetch={async () => {
        await other.refetch();
      }}
    >
      {email}
    </UpdateUserProfileFormControl>
  );
};
