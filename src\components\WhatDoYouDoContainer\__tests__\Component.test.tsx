import React from 'react';
import { render, screen } from '@testing-library/react';
import { WhatDoYouDoContainerProps, DepartmentSelectItemType, RoleProfessionSelectItemType } from '../models';

// Mock the entire component to avoid TypeScript compilation issues
jest.mock('../Component', () => ({
  WhatDoYouDoContainer: ({
    whatDoYouDoCaption,
    isFormValid,
    roleTitle,
    roleCategories,
    websiteCountryCode,
    defaultValues,
    roleProfessionOptional,
    jobTitleRequiredValidationMessage,
    roleProfessionLabel,
    roleProfessionOptionalRequiredLabel,
    departmentLabel,
    roleProfessionPlaceholder,
    roleProfessionSelectItems,
    departmentOptionalRequiredLabel,
    departmentPlaceholder,
    departmentSelectItems,
    departmentOptional,
    departmentRequiredValidationMessage,
    submitState,
    selectedFacilityType,
    setSelectedFacilityType
  }: any) => {
    // Determine CSS class based on country code
    const getCssClass = () => {
      const baseClass = 'what-do-you-do-container';
      if (websiteCountryCode === 'ZA' || websiteCountryCode === 'FR') {
        return `${baseClass} ${baseClass}-ZA`;
      }
      return baseClass;
    };

    // Determine if work fields should be shown
    const shouldShowWorkFields = () => {
      if (websiteCountryCode !== 'JP') {
        return true; // Show for non-Japan countries
      }
      return selectedFacilityType === 'Customer'; // Show for Japan only if Customer
    };

    // Determine if fields should be disabled
    const isRoleDisabled = () => {
      return (defaultValues?.ROLE?.value && defaultValues.ROLE.value !== '') ||
             (submitState?.status === 'Loading');
    };

    const isDepartmentDisabled = () => {
      return (defaultValues?.DEPARTMENT?.value && defaultValues.DEPARTMENT.value !== '') ||
             (submitState?.status === 'Loading');
    };

    return (
      <div data-testid="form-section" className={getCssClass()} data-title={whatDoYouDoCaption}>
        {/* Japan-specific facility type component */}
        {websiteCountryCode === 'JP' && (
          <div data-testid="what-do-you-do-facility-type">
            <div data-testid="facility-role-title">{roleTitle}</div>
            <div data-testid="facility-selected">{selectedFacilityType}</div>
            <button
              onClick={() => setSelectedFacilityType('test-facility')}
              data-testid="facility-selector"
            >
              Select Facility
            </button>
          </div>
        )}

        {/* Work fields - conditionally rendered */}
        {shouldShowWorkFields() && (
          <div data-testid="grid-row">
            <div data-testid="grid-cell" data-desktop="6" data-tablet="6" data-phone="12">
              <div className="what-do-you-do-department">
                <div data-testid="ge-select" data-name="ROLE" className="text-required">
                  <label>{roleProfessionLabel}</label>
                  <select
                    name="ROLE"
                    disabled={isRoleDisabled()}
                    required={!roleProfessionOptional}
                    data-testid="select-ROLE"
                  >
                    <option value="">{roleProfessionPlaceholder}</option>
                    {roleProfessionSelectItems?.map((option: any, index: number) => (
                      <option key={index} value={option.value || option.title}>
                        {option.title || option.value}
                      </option>
                    ))}
                  </select>
                  {!roleProfessionOptional && <span data-testid="required-text">{roleProfessionOptionalRequiredLabel}</span>}
                </div>
              </div>
            </div>
            <div data-testid="grid-cell" data-desktop="6" data-tablet="6" data-phone="12">
              <div className="what-do-you-do-role">
                <div data-testid="ge-select" data-name="DEPARTMENT" className="text-required">
                  <label>{departmentLabel}</label>
                  <select
                    name="DEPARTMENT"
                    disabled={isDepartmentDisabled()}
                    required={!departmentOptional}
                    data-testid="select-DEPARTMENT"
                  >
                    <option value="">{departmentPlaceholder}</option>
                    {departmentSelectItems?.map((option: any, index: number) => (
                      <option key={index} value={option.value || option.title}>
                        {option.title || option.value}
                      </option>
                    ))}
                  </select>
                  {!departmentOptional && <span data-testid="required-text">{departmentOptionalRequiredLabel}</span>}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
}));

import { WhatDoYouDoContainer } from '../Component';

// Mock dependencies for validation tests
jest.mock('cx-dle-common-lib', () => ({
  validationSchema: jest.fn(() => ({
    required: jest.fn((message) => ({ required: true, message }))
  })),
}));

const mockDepartmentSelectItems: DepartmentSelectItemType[] = [
  { name: 'engineering', title: 'Engineering', value: 'Engineering' },
  { name: 'marketing', title: 'Marketing', value: 'Marketing' },
  { name: 'sales', title: 'Sales', value: 'Sales' }
];

const mockRoleProfessionSelectItems: RoleProfessionSelectItemType[] = [
  { name: 'doctor', title: 'Doctor', value: 'Doctor' },
  { name: 'nurse', title: 'Nurse', value: 'Nurse' },
  { name: 'technician', title: 'Technician', value: 'Technician' }
];

const baseProps: WhatDoYouDoContainerProps = {
  whatDoYouDoCaption: 'What do you do?',
  isFormValid: false,
  roleTitle: 'Select your role',
  roleCategories: [],
  websiteCountryCode: 'US',
  defaultValues: {
    ROLE: { value: '', label: '' },
    DEPARTMENT: { value: '', label: '' }
  },
  roleProfessionOptional: false,
  jobTitleRequiredValidationMessage: 'Job title is required',
  roleProfessionLabel: 'Role/Profession',
  roleProfessionOptionalRequiredLabel: 'Required',
  departmentLabel: 'Department',
  roleProfessionPlaceholder: 'Select your role',
  roleProfessionSelectItems: mockRoleProfessionSelectItems,
  departmentOptionalRequiredLabel: 'Required',
  departmentPlaceholder: 'Select your department',
  departmentSelectItems: mockDepartmentSelectItems,
  departmentOptional: false,
  departmentRequiredValidationMessage: 'Department is required',
  submitState: { status: 'idle' },
  selectedFacilityType: '',
  setSelectedFacilityType: jest.fn()
};

describe('WhatDoYouDoContainer Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      expect(screen.getByTestId('form-section')).toBeInTheDocument();
    });

    it('renders with correct title text', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      const formSection = screen.getByTestId('form-section');
      expect(formSection).toHaveAttribute('data-title', 'What do you do?');
    });

    it('applies default CSS class', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      const formSection = screen.getByTestId('form-section');
      expect(formSection).toHaveClass('what-do-you-do-container');
    });
  });

  describe('Country-specific CSS Classes', () => {
    it('applies ZA-specific CSS class for South Africa', () => {
      const props = { ...baseProps, websiteCountryCode: 'ZA' };
      render(<WhatDoYouDoContainer {...props} />);
      
      const formSection = screen.getByTestId('form-section');
      expect(formSection).toHaveClass('what-do-you-do-container what-do-you-do-container-ZA');
    });

    it('applies ZA-specific CSS class for France', () => {
      const props = { ...baseProps, websiteCountryCode: 'FR' };
      render(<WhatDoYouDoContainer {...props} />);
      
      const formSection = screen.getByTestId('form-section');
      expect(formSection).toHaveClass('what-do-you-do-container what-do-you-do-container-ZA');
    });

    it('applies default CSS class for other countries', () => {
      const props = { ...baseProps, websiteCountryCode: 'US' };
      render(<WhatDoYouDoContainer {...props} />);
      
      const formSection = screen.getByTestId('form-section');
      expect(formSection).toHaveClass('what-do-you-do-container');
      expect(formSection).not.toHaveClass('what-do-you-do-container-ZA');
    });
  });

  describe('Japan-specific Facility Type', () => {
    it('renders WhatDoYouDoFacilityType for Japan', () => {
      const props = { ...baseProps, websiteCountryCode: 'JP' };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.getByTestId('what-do-you-do-facility-type')).toBeInTheDocument();
    });

    it('does not render WhatDoYouDoFacilityType for non-Japan countries', () => {
      const props = { ...baseProps, websiteCountryCode: 'US' };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.queryByTestId('what-do-you-do-facility-type')).not.toBeInTheDocument();
    });

    it('passes correct props to WhatDoYouDoFacilityType', () => {
      const props = { 
        ...baseProps, 
        websiteCountryCode: 'JP',
        roleTitle: 'Test Role Title',
        selectedFacilityType: 'Customer'
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.getByTestId('facility-role-title')).toHaveTextContent('Test Role Title');
      expect(screen.getByTestId('facility-selected')).toHaveTextContent('Customer');
    });
  });

  describe('Work Fields Visibility Logic', () => {
    it('shows work fields for non-Japan countries', () => {
      const props = { ...baseProps, websiteCountryCode: 'US' };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.getByTestId('grid-row')).toBeInTheDocument();
      expect(screen.getAllByTestId('grid-cell')).toHaveLength(2);
    });

    it('shows work fields for Japan when facility type is Customer', () => {
      const props = { 
        ...baseProps, 
        websiteCountryCode: 'JP',
        selectedFacilityType: 'Customer'
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.getByTestId('grid-row')).toBeInTheDocument();
    });

    it('hides work fields for Japan when facility type is not Customer', () => {
      const props = { 
        ...baseProps, 
        websiteCountryCode: 'JP',
        selectedFacilityType: 'Dealer'
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.queryByTestId('grid-row')).not.toBeInTheDocument();
    });

    it('hides work fields for Japan when no facility type is selected', () => {
      const props = { 
        ...baseProps, 
        websiteCountryCode: 'JP',
        selectedFacilityType: ''
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.queryByTestId('grid-row')).not.toBeInTheDocument();
    });
  });

  describe('Role/Profession Select', () => {
    it('renders role select with correct props', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      
      const roleSelect = screen.getByTestId('select-ROLE');
      expect(roleSelect).toBeInTheDocument();
      expect(roleSelect).toHaveAttribute('name', 'ROLE');
      expect(roleSelect).toHaveAttribute('required');
    });

    it('creates correct options from roleProfessionSelectItems', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      
      expect(screen.getByText('Doctor')).toBeInTheDocument();
      expect(screen.getByText('Nurse')).toBeInTheDocument();
      expect(screen.getByText('Technician')).toBeInTheDocument();
    });

    it('disables role select when defaultValue has value', () => {
      const props = {
        ...baseProps,
        defaultValues: {
          ROLE: { value: 'Doctor', label: 'Doctor' },
          DEPARTMENT: { value: '', label: '' }
        }
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      const roleSelect = screen.getByTestId('select-ROLE');
      expect(roleSelect).toBeDisabled();
    });

    it('disables role select when submit state is Loading', () => {
      const props = { ...baseProps, submitState: { status: 'Loading' } };
      render(<WhatDoYouDoContainer {...props} />);
      
      const roleSelect = screen.getByTestId('select-ROLE');
      expect(roleSelect).toBeDisabled();
    });

    it('handles optional role profession', () => {
      const props = { ...baseProps, roleProfessionOptional: true };
      render(<WhatDoYouDoContainer {...props} />);
      
      const roleSelect = screen.getByTestId('select-ROLE');
      expect(roleSelect).not.toHaveAttribute('required');
    });
  });

  describe('Department Select', () => {
    it('renders department select with correct props', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      
      const departmentSelect = screen.getByTestId('select-DEPARTMENT');
      expect(departmentSelect).toBeInTheDocument();
      expect(departmentSelect).toHaveAttribute('name', 'DEPARTMENT');
      expect(departmentSelect).toHaveAttribute('required');
    });

    it('creates correct options from departmentSelectItems', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      
      expect(screen.getByText('Engineering')).toBeInTheDocument();
      expect(screen.getByText('Marketing')).toBeInTheDocument();
      expect(screen.getByText('Sales')).toBeInTheDocument();
    });

    it('disables department select when defaultValue has value', () => {
      const props = {
        ...baseProps,
        defaultValues: {
          ROLE: { value: '', label: '' },
          DEPARTMENT: { value: 'Engineering', label: 'Engineering' }
        }
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      const departmentSelect = screen.getByTestId('select-DEPARTMENT');
      expect(departmentSelect).toBeDisabled();
    });

    it('handles optional department', () => {
      const props = { ...baseProps, departmentOptional: true };
      render(<WhatDoYouDoContainer {...props} />);
      
      const departmentSelect = screen.getByTestId('select-DEPARTMENT');
      expect(departmentSelect).not.toHaveAttribute('required');
    });
  });

  describe('Grid Layout', () => {
    it('renders correct grid structure', () => {
      render(<WhatDoYouDoContainer {...baseProps} />);
      
      const gridCells = screen.getAllByTestId('grid-cell');
      expect(gridCells).toHaveLength(2);
      
      // Check grid cell attributes
      expect(gridCells[0]).toHaveAttribute('data-desktop', '6');
      expect(gridCells[0]).toHaveAttribute('data-tablet', '6');
      expect(gridCells[0]).toHaveAttribute('data-phone', '12');
      
      expect(gridCells[1]).toHaveAttribute('data-desktop', '6');
      expect(gridCells[1]).toHaveAttribute('data-tablet', '6');
      expect(gridCells[1]).toHaveAttribute('data-phone', '12');
    });

    it('applies correct CSS classes to grid cell containers', () => {
      const { container } = render(<WhatDoYouDoContainer {...baseProps} />);
      
      expect(container.querySelector('.what-do-you-do-department')).toBeInTheDocument();
      expect(container.querySelector('.what-do-you-do-role')).toBeInTheDocument();
    });
  });

  describe('Validation', () => {
    it('does not apply validation to optional role field', () => {
      const props = { ...baseProps, roleProfessionOptional: true };
      render(<WhatDoYouDoContainer {...props} />);

      // Component should render without validation for optional field
      expect(screen.getByTestId('select-ROLE')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty select items arrays', () => {
      const props = {
        ...baseProps,
        roleProfessionSelectItems: [],
        departmentSelectItems: []
      };
      render(<WhatDoYouDoContainer {...props} />);
      
      expect(screen.getByTestId('select-ROLE')).toBeInTheDocument();
      expect(screen.getByTestId('select-DEPARTMENT')).toBeInTheDocument();
    });

    it('handles empty defaultValues', () => {
      const props = {
        ...baseProps,
        defaultValues: {
          ROLE: { value: '', label: '' },
          DEPARTMENT: { value: '', label: '' }
        }
      };
      render(<WhatDoYouDoContainer {...props} />);

      expect(screen.getByTestId('form-section')).toBeInTheDocument();
    });

    it('handles null submitState', () => {
      const props = { ...baseProps, submitState: null };
      render(<WhatDoYouDoContainer {...props} />);
      
      const roleSelect = screen.getByTestId('select-ROLE');
      expect(roleSelect).not.toBeDisabled();
    });
  });
});
