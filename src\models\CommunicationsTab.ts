// This file is auto-generated. Do not edit manually.

export interface CommunicationsTab {
  communicationPreferences: CommunicationPreferenceType[];
}

export interface CommunicationPreferenceType {
  name: string;
  infoTitle: string;
  saveButtonText: string;
  editLinkText: string;
  productType: ProductTypeType;
  infoDescription: string;
  notificationType: NotificationTypeType;
  discardButtonText: string;
  groups: GroupType[];
  closeLinkText: string;
  preferencesThreshold: number;
  supportedCountries: string[];
}

export interface ProductTypeType {
  name: string;
  value: string;
  code: string;
}

export interface NotificationTypeType {
  name: string;
  title: string;
  value: string;
  code: string;
}

export interface GroupType {
  name: string;
  categoryDefinition: string;
  disableToggle: boolean;
  productType: ProductTypeType;
  groupTitle: string;
  onLabelText: string;
  offLabelText: string;
  items: ItemType[];
}

export interface ProductTypeType {
  name: string;
  value: string;
  code: string;
}

export interface ItemType {
  name: string;
  title: string;
  code: string;
  richDescription: string;
  disableSelection: boolean;
  emailPreferenceDescription: string;
  description: string;
}