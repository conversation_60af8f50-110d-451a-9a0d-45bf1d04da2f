import { render, screen, fireEvent } from '@testing-library/react';
import { PhoneSection } from '../Component';
import { FORM_FIELDS } from '../constants';

jest.mock('../constants', () => ({
  FORM_FIELDS: {
    PHONE: 'PHONE',
    EXTENSION: 'EXTENSION',
  },
}));

jest.mock('../../../../Common/UpdateUserProfileForm', () => {
  return function MockUpdateUserProfileForm(props: any) {
    const handleSubmit = (e: any) => {
      e.preventDefault();
      if (props.refetch) {
        props.refetch();
      }
    };

    return (
      <div data-testid="mock-form" id={props.id}>
        {props.children}
        <input data-testid="phone-input" name={FORM_FIELDS.PHONE} />
        {props.extension && <input data-testid="extension-input" name={FORM_FIELDS.EXTENSION} />}
        <form onSubmit={handleSubmit}>
          <button type="submit">Submit</button>
        </form>
      </div>
    );
  };
});

const mockProps = {
  phone: '1234567890',
  phoneLabelText: 'Phone',
  phoneSectionDescription: 'Enter your phone details',
  emptyNewPhoneErrorMessage: 'Phone is required',
  newPhoneLabelText: 'New Phone',
  newPhoneHelperText: 'Enter your new phone number',
  newPhonePlaceholderText: 'Enter phone number',
  updateConfirmationMessage: 'Phone updated successfully',
  requiredLabelText: 'Required',
  formName: 'testForm',
  subFormName: 'phoneSection',
  phoneExtension: '123',
  countryCode: 'US',
  filteredCountryList: [
    {
      name: 'United States',
      maxLength: '10',
      countryPhoneNumberPlaceholder: '(*************',
      flagSource: 'us-flag.png',
      validationMessage: 'Invalid phone number',
      minLength: '10',
      countryCode: 'US',
      countryISDCode: '+1',
      countryName: 'United States',
    },
  ],
  extension: {
    phoneExtensionLabelText: 'Extension',
    phoneExtensionOptionLabel: 'Optional',
    validations: [
      {
        maxCharacters: 5,
        regex: /^[0-9]*$/,
        supportedCountries: ['US'],
        validCode: ['1', '2', '3', '4', '5'],
      },
    ],
  },
  closeButtonText: 'Close',
  editButtonText: 'Edit',
  saveButtonText: 'Save',
  cancelButtonText: 'Cancel',
  siteName: 'Test Site',
  refetch: jest.fn(),
};

describe('PhoneSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the phone section with provided phone number', () => {
    render(<PhoneSection {...mockProps} />);

    expect(screen.getByText(mockProps.phone)).toBeInTheDocument();
    expect(screen.getByTestId('mock-form')).toBeInTheDocument();
  });

  it('renders when filteredCountryList is provided', () => {
    render(<PhoneSection {...mockProps} />);
    expect(screen.getByTestId('mock-form')).toBeInTheDocument();
  });

  it('includes extension field when phone extension is provided', () => {
    render(<PhoneSection {...mockProps} />);

    const form = screen.getByTestId('mock-form');
    expect(form).toHaveAttribute('id', 'PHONE');
    expect(screen.getByTestId('extension-input')).toBeInTheDocument();
  });

  it('configures form fields correctly', () => {
    render(<PhoneSection {...mockProps} />);

    expect(screen.getByTestId('phone-input')).toBeInTheDocument();
    expect(screen.getByTestId('extension-input')).toBeInTheDocument();
  });

  it('handles refetch callback', async () => {
    render(<PhoneSection {...mockProps} />);

    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    expect(mockProps.refetch).toHaveBeenCalled();
  });

  it('renders without extension field when no extension is provided', () => {
    const propsWithoutExtension = {
      ...mockProps,
      phoneExtension: undefined,
    };

    render(<PhoneSection {...propsWithoutExtension} />);

    expect(screen.queryByTestId('extension-input')).not.toBeInTheDocument();
  });

  it('applies correct validation rules based on country code', () => {
    render(<PhoneSection {...mockProps} />);

    const form = screen.getByTestId('mock-form');
    expect(form).toHaveAttribute('id', 'PHONE');
  });

  it('handles multiple country codes in filteredCountryList', () => {
    const propsWithMultipleCountries = {
      ...mockProps,
      filteredCountryList: [
        ...mockProps.filteredCountryList,
        {
          name: 'Canada',
          maxLength: '10',
          countryPhoneNumberPlaceholder: '(*************',
          flagSource: 'ca-flag.png',
          validationMessage: 'Invalid phone number',
          minLength: '10',
          countryCode: 'CA',
          countryISDCode: '+1',
          countryName: 'Canada',
        },
      ],
    };

    render(<PhoneSection {...propsWithMultipleCountries} />);
    expect(screen.getByTestId('mock-form')).toBeInTheDocument();
  });

  it('updates form fields when phone or extension changes', () => {
    const { rerender } = render(<PhoneSection {...mockProps} />);

    expect(screen.getByTestId('phone-input')).toBeInTheDocument();

    const updatedProps = {
      ...mockProps,
      phone: '9876543210',
      phoneExtension: '999',
    };

    rerender(<PhoneSection {...updatedProps} />);
    expect(screen.getByTestId('phone-input')).toBeInTheDocument();
  });

  it('handles extension validation based on country code', () => {
    const propsWithCustomExtensionValidation = {
      ...mockProps,
      extension: {
        ...mockProps.extension,
        validations: [
          {
            maxCharacters: 3,
            regex: /^[0-9]*$/,
            supportedCountries: ['US'],
            validCode: ['1', '2', '3'],
          },
        ],
      },
    };

    render(<PhoneSection {...propsWithCustomExtensionValidation} />);
    expect(screen.getByTestId('extension-input')).toBeInTheDocument();
  });

  it('handles edge case with empty phone number', () => {
    const propsWithEmptyPhone = {
      ...mockProps,
      phone: '',
      phoneExtension: '',
    };

    render(<PhoneSection {...propsWithEmptyPhone} />);
    expect(screen.getByTestId('mock-form')).toBeInTheDocument();
  });
});
