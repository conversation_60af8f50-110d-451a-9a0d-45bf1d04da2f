import { Maybe } from '../../PersonalInformation/models';
import { AlertMessageType } from 'cx-dle-component-library';
import { InformationFormField } from '../InformationEditForm/models';
import { FormValues } from 'cx-dle-common-lib';
import { CountryCodeDropDowType } from '../../PersonalInformation/components/PhoneSection/models';

export interface AlertState {
  text: string;
  type: AlertMessageType;
}

export interface UpdateUserProfileFormOwnProps {
  sectionTitle: string;
  sectionDescription: string;
  sectionStatusInfo?: string;

  closeButtonText: string;
  editButtonText: string;
  saveButtonText: string;
  cancelButtonText: string;
  updateConfirmationMessage: string;

  formFieldsConfiguration: InformationFormField[];
  name?: string;
  successAlertType?: AlertMessageType;
  defaultHideToggle?: boolean;

  getMutationArgs: (values: FormValues) => InputUpdateUserProfileGraphType;
  refetch: () => Promise<void>;
  handleCancel?: () => void;

  formName: string;
  subFormName: string;

  children?: React.ReactNode;

  countryCodeDropDown?: [
    {
      name: string;
      flagSource: string;
      minLength: string;
      countryCode: string;
      countryISDCode: string;
      countryName: string;
      maxLength: string;
      validationMessage: string;
      isAvailableInPackage?: boolean;
    },
  ];
  siteName?: string;
  setAlert?: () => void;
  alert?: string;
  userInfo?: {
    userAccountDetails: UserAccountDetails;
  };
  id?: string;
  extension?: string;
  sectionExtensionTitle?: string;
  filteredCountryList: CountryCodeDropDowType[];
  validatedCountries: string[];
}

export interface UserAccountDetails {
  assignedApplications: [];
  progressiveRegistrations: [];
  userName: string;
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  region: string;
  country: string;
  role: string;
  language: string;
  isHoldingAccount: boolean;
  contactPhone: string;
  extension: string;
  accountName: string;
  department: string;
  facilityType: string;
  termsAndConditions: boolean;
  __typename: string;
}

export interface UpdateUserProfileFormProps extends UpdateUserProfileFormOwnProps {}

export namespace UpdateUserProfile {
  export type Variables = {
    mutationArguments: InputUpdateUserProfileGraphType;
  };

  export type Mutation = {
    __typename?: 'Mutation';

    updateUserProfile: Maybe<UpdateUserProfile>;
  };

  export type UpdateUserProfile = {
    __typename?: 'UpdateUserProfileResultGraphType';

    isSuccess: boolean;

    message: Maybe<string>;

    status: string;
  };
}

export interface InputUpdateUserProfileGraphType {
  firstName?: Maybe<string>;

  lastName?: Maybe<string>;

  localFirstName?: Maybe<string>;

  localLastName?: Maybe<string>;

  phone?: Maybe<string>;

  language?: Maybe<string>;

  emailToBeUpdated?: Maybe<string>;

  roleProfession?: Maybe<string>;

  department?: Maybe<string>;

  organization?: Maybe<string>;

  organizationType?: Maybe<string>;

  shippingAddressLine1?: Maybe<string>;

  shippingAddressLine2?: Maybe<string>;

  shippingAddressLine3?: Maybe<string>;

  shippingCountry?: Maybe<string>;

  shippingCity?: Maybe<string>;

  shippingState?: Maybe<string>;

  shippingZipCode?: Maybe<string>;

  billingAddressLine1?: Maybe<string>;

  billingAddressLine2?: Maybe<string>;

  billingAddressLine3?: Maybe<string>;

  billingCountry?: Maybe<string>;

  billingCity?: Maybe<string>;

  billingState?: Maybe<string>;

  billingZipCode?: Maybe<string>;
}
