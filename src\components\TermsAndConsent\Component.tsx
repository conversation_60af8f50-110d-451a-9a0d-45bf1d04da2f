import { FormSection, RichText } from 'cx-dle-component-library';
import { FORM_FIELDS } from '../../constants';
import { validationSchema } from 'cx-dle-common-lib';
import GeCheckbox from '../Common/GeCheckbox';

export const TermsAndConsent = (props: any) => {

  // Handle checkbox change
  const handleChangeCheckbox = () => {
    props.changeHandler();
  };

  return (
    <FormSection className="terms-and-conditions" titleText=''>
      <ul className="terms-list">
        <li className="terms-list-item">
          <GeCheckbox
            onClick={handleChangeCheckbox}
            name={FORM_FIELDS.TERMS_AND_CONDITIONS}
            labelName={props.termsAndConditionsLabel}
            defaultValue = {props?.defaultValues[FORM_FIELDS.TERMS_AND_CONDITIONS]}
            validate={validationSchema().required()}
            className={props?.isFormValid ? 'one-registration-form-error' : ''}
          >
            <RichText text={props.termsAndConditionsLabel} />
          </GeCheckbox>
        </li>
      </ul>
    </FormSection>
  );
};
