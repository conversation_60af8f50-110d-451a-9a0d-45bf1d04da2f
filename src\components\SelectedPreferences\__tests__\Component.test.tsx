import React from 'react';
import { render, screen } from '@testing-library/react';
import { SelectedPreferences } from '../Component';
import { SelectedPreferencesProps } from '../models';

describe('SelectedPreferences', () => {
  const defaultProps: SelectedPreferencesProps = {
    preferences: ['Preference 1', 'Preference 2', 'Preference 3', 'Preference 4'],
    preferencesThreshold: 3,
  };

  const renderComponent = (props: Partial<SelectedPreferencesProps> = {}) => {
    return render(<SelectedPreferences {...defaultProps} {...props} />);
  };

  describe('Regular Preferences Display', () => {
    it('should not show badge when preferences are less than threshold', () => {
      renderComponent({
        preferences: ['Preference 1', 'Preference 2'],
      });

      expect(screen.queryByText('+')).not.toBeInTheDocument();
    });
  });

  describe('Department Preferences Display', () => {
    const departmentProps = {
      ...defaultProps,
      isDepartment: true,
      topFollowingCount: 5,
      totalDepartmentPreferredCount: 2,
    };

    it('should not show department badge when preferred count is 0', () => {
      renderComponent({
        ...departmentProps,
        topFollowingCount: 2,
        totalDepartmentPreferredCount: 2,
      });

      expect(screen.queryByText('+')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty preferences array', () => {
      renderComponent({
        preferences: [],
      });

      expect(screen.queryByTestId('ge-selected-preferences')).not.toBeInTheDocument();
    });

    it('should handle undefined preferences', () => {
      renderComponent({
        preferences: undefined,
      });

      expect(screen.queryByTestId('ge-selected-preferences')).not.toBeInTheDocument();
    });
  });
});
