import { NotificationsPreferencesProvider } from '../../context/NotificationsPreferencesContext';
import { CommunicationsTab as CommunicationsTabProps } from '../../models/CommunicationsTab';
import ServiceNotificationPreferences from '../ServiceNotificationPreferences';

export const CommunicationsTab = (props: CommunicationsTabProps) => {
  return (
    <>
      {props?.communicationPreferences?.map((preference) => (
        <NotificationsPreferencesProvider>
          <ServiceNotificationPreferences {...preference} />
        </NotificationsPreferencesProvider>
      ))}
    </>
  );
};
