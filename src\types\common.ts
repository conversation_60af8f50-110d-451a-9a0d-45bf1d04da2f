import { NotificationType } from '../components/ServiceNotificationPreferences/models';
import { SfdcNotificationsFlagsLocalizationType } from '../models/Sitecommoncontent';

export type LanguageContextType = {
  language: string;
  setLanguage: (lang: string) => void;
};

export type NotificationsPreferencesContextType = {
  preferences: NotificationType[];
  updateNotificationPreferences: (newPreferences: NotificationType[]) => void;
  selectedPreferences: string[];
  setSelectedPreferences: (
    notificationTypeList: NotificationType[],
    channelName: string,
    sfdcNotificationsFlagsLocalization: SfdcNotificationsFlagsLocalizationType,
  ) => void;
  setDataSaved: (dataSaved: boolean) => void;
  isDataSaved: boolean;
};
