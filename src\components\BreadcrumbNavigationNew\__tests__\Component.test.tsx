
import React from 'react';
import { render, screen } from '@testing-library/react';
import { BreadcrumbProps } from '../models';
import BreadcrumbControl from '../Component';


jest.mock('cx-dle-component-library', () => ({
    // Mock other components if needed
    BreadcrumbComponent: ({ navigationTitle }: any) => (
        <div>{navigationTitle}</div>
    ),
    GridContainer: ({ children }: any) => <div>{children}</div>,
    GridRow: ({ children }: any) => <div>{children}</div>,
    GridCell: ({ children }: any) => <div>{children}</div>,
}));


describe('BreadcrumbControl', () => {
    const defaultProps: BreadcrumbProps = {
        breadcrumbLink: '/home',
        breadcrumbNavigationTitle: 'Home',
        breadcrumbPageTitle: 'Dashboard',
    };

    it('should render BreadcrumbComponent when required props are provided', () => {
        render(<BreadcrumbControl {...defaultProps} />);

        const breadcrumb = screen.getByText('Home');
        expect(breadcrumb).toBeInTheDocument();
    });

    it('should not render anything if breadcrumbLink is missing', () => {
        const props = {
            ...defaultProps,
            breadcrumbLink: '',
        };
        const { container } = render(<BreadcrumbControl {...props} />);
        expect(container.firstChild).toBeNull();
    });

    it('should not render anything if breadcrumbNavigationTitle is missing', () => {
        const props = {
            ...defaultProps,
            breadcrumbNavigationTitle: '',
        };
        const { container } = render(<BreadcrumbControl {...props} />);
        expect(container.firstChild).toBeNull();
    });

});
