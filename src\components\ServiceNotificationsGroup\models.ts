import { SfdcNotificationsFlagsLocalizationType } from '../../models/Sitecommoncontent';
import {
  GroupCheckList,
  NotificationPreferencesState,
  NotificationType,
} from '../ServiceNotificationPreferences/models';

import { ApolloError, DocumentNode } from '@apollo/client';

export interface ServiceNotificationsGroupProps {
  settings: GroupCheckList[];
  onError: (error: ApolloError) => void;
  onChange: (UpdatedPreferences: GroupCheckList[]) => void;
  onSave: (saved: boolean) => void;
  discardButtonText: string;
  saveButtonText: string;
  notificationType?: NotificationType[];
  notificationChannel?: string;
  categoryDefinition: string[];
  completeList: NotificationType[];
  showErrorSnackbar?: () => void;
  sfdcNotificationsFlagsLocalization?: SfdcNotificationsFlagsLocalizationType;
}

export interface ServiceNotificationsGroupControlProps extends ServiceNotificationsGroupProps {}

export interface ServiceNotificationsGroupState {
  ShowCSMErrorBar: boolean;
}

export type UpdatePreferencesFn = (
  preferences: Array<{
    appName: string;
    notificationCode: string;
    notificationChannel: string;
  }>,
) => void;

export interface UpdateNotificationPreferencesMutationProps {
  notificationTypesQuery: DocumentNode;
  children: (updatePreferences: UpdatePreferencesFn) => React.ReactNode;
  onError?: (error: ApolloError) => void;
  hasPreferencesChanged: (input: NotificationType[]) => void;
  client: any;
}

export interface NotificationTypePreference {
  appName: string;

  preferred?: boolean;

  notificationCode: string;

  notificationChannel: string;
}

export interface UserPreferenceInput {
  onboarded?: boolean;

  mobileOnboarded?: boolean;

  allowNotifications?: boolean;

  notificationType?: NotificationTypePreference[];
}

export namespace UpdateNotificationPreference {
  export type Variables = {
    input: UserPreferenceInput;
  };

  export type Mutation = {
    __typename?: 'Mutation';

    updateUserPreferences: UpdateUserPreferences;
  };

  export type UpdateUserPreferences = {
    __typename?: 'UserPreference';

    notificationType: NotificationType[];
  };

  export type NotificationType = {
    __typename?: 'NotificationType';
    appName: string;
    notificationCode: string;
    notificationChannel: string;
    preferred: boolean;
  };
}
