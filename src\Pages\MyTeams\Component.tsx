import { useEffect, useMemo, useState } from 'react';
import { EDSIcon, GeTabButton, GridCell, GridContainer, GridRow, RichText, Text } from 'cx-dle-component-library';
import { usePageData, useUserInfo } from '../../hooks/common';
import { Myteam } from '../../models/Myteam';
import './styles.scss';
import { useLazyQuery } from '@apollo/client/react';
import { myTeamQuery } from './query';
import { OMNITURE_EVENTS, prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import ContactPopupComponent from './ContactPopup';
import NoResults from './NoResults';
import SkeletonLoader from '../../components/Common/SkeletonLoader';
import { useUserCountry } from '../../hooks/common/useUserCountry';
import { UserCountry } from '../../components/Common/InformationEditForm/models';
import ServiceUnavailabilityView from './ServiceUnavailabilityView';
import { Metadata } from '../../components/Metadata';

import { decodeIdToken } from '../../utils/commonUtils';
import { IdToken } from '../../models/Idtoken';
import { isCSMMode } from '../../utils';
import BreadcrumbControl from '../../components/BreadcrumbNavigationNew/Component';

const MyTeams = () => {
  const [country, setCountry] = useState<any>('');
  const [activeTab, setActiveTab] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const { data: pageData, isLoading: pageDataLoading } = usePageData<Myteam>('myteam', 'myteamData');
  const [getMyTeam, { loading: myTeamLoading, data: myTeamData, error: myTeamError }] = useLazyQuery(myTeamQuery);
  const [teamValue, setTeamValue] = useState({});
  const [isOpenPopup, setIsOpenPopup] = useState(false);

  const { data: userCountry } = useUserCountry<UserCountry>('country');

  // Declare the variable globally
  let userInfo: any, userInfoError: any;

  const siteName = window.location.pathname;
  const siteDomain = window.location.hostname;

  const decodedToken = decodeIdToken() as IdToken;

  let userMailingCountry;

  if (isCSMMode()) {
    const { data, error } = useUserInfo();
    userInfo = data;
    userInfoError = error;
    userMailingCountry = userInfo?.userAccountDetails?.country;
  } else {
    userMailingCountry = decodedToken?.address?.country;
  }

  if (!userMailingCountry) {
    userMailingCountry = userCountry?.data?.websiteCountryCode || 'US';
  }

  const getCountry = async () => {
    if (userMailingCountry) {
      setCountry(userMailingCountry);
    }
  };

  useEffect(() => {
    if (!myTeamLoading && myTeamData) {
      setIsLoading(false);
    }
  }, [myTeamLoading, myTeamData]);

  useEffect(() => {
    if (!country) return;
    getMyTeam({
      variables: { countryCode: country },
    });
  }, [country]);

  useEffect(() => {
    if (pageData?.myTeam?.myTeamTabList?.length) {
      setActiveTab(pageData.myTeam.myTeamTabList[0]?.tabName);
    }
  }, [pageData]);

  useEffect(() => {
    if (userMailingCountry) {
      getCountry();
    }
  }, [userMailingCountry, siteName, siteDomain]);

  const filteredTeamMembers = useMemo(() => {
    const selectedTab: any = pageData?.myTeam?.myTeamTabList.find((tab) => {
      return tab.tabName === activeTab;
    });

    const countrybasedRole = selectedTab?.role?.find(
      (item) => item.countryCode.toLowerCase() === country?.toLowerCase(),
    );

    const roleLabel = countrybasedRole ? countrybasedRole?.label : selectedTab?.role[0]['label'];

    const filterTeamMembers = myTeamData?.team?.members.filter((teamMember: any) => {
      return teamMember.anaplanRole === null
        ? roleLabel?.split('|').includes(teamMember?.role) + ' '
        : roleLabel?.split('|').includes(teamMember?.anaplanRole);
    });

    return filterTeamMembers;
  }, [activeTab, myTeamData?.team?.members]);

  const handleOpenPopup = (item: any) => {
    setIsOpenPopup(true);
    setTeamValue(item);
  };

  const cardLoader = {
    rows: [
      {
        className: 'teams-skeleton__columns-card',
        columns: [
          { width: '45px', height: '45px', margin: '0 0 42px 0' },
          {
            rows: [
              {
                className: 'teams-skeleton__columns-card--content',
                columns: [
                  { width: '280px', height: '24px', margin: '0 0 7px 0' },
                  { width: '280px', height: '24px', margin: '0 0 13px 0' },
                  { width: '67px', height: '24px', margin: '0 0 0 0' },
                ],
              },
            ],
          },
        ],
      },
    ],
  };

  const skeletonBlocks = (
    <div>
      <SkeletonLoader
        list={[
          {
            rows: [{ width: '335px', height: '35px', margin: '0 0 42px' }],
          },
          {
            rows: [
              {
                className: 'teams-skeleton__columns',
                columns: [cardLoader, cardLoader, cardLoader, cardLoader, cardLoader],
              },
            ],
          },
        ]}
      />
    </div>
  );

  if (pageDataLoading) {
    return null;
  }

  return (
    <>
      {pageData?.metadata && <Metadata metadata={pageData.metadata} />}
      <BreadcrumbControl
        breadcrumbLink={pageData?.breadcrumb.breadcrumbLink.href ?? ''}
        breadcrumbNavigationTitle={pageData?.breadcrumb.breadcrumbLink.text ?? ''}
        breadcrumbPageTitle={pageData?.breadcrumb.breadcrumbPageTitle ?? ''}
      ></BreadcrumbControl>
      <div className="ge-heading-content__holder">
        <div className="ge-heading-content__main-content">
          <GridContainer>
            <Text key={pageData?.pageTitle} text={pageData?.pageTitle} className="ge-heading-content__title" tag="h2" />
            <GridCell desktop={12} tablet={8} phone={4}>
              <RichText text={pageData?.teamMember} />
            </GridCell>
          </GridContainer>
        </div>
      </div>
      <GridContainer className={`teams-container ${isLoading ? 'loading-row-1' : ''}`}>
        {isLoading && !myTeamError && !userInfoError && (
          <div style={{ display: 'flex', flexDirection: 'row' }}>{skeletonBlocks}</div>
        )}
        {!isLoading && filteredTeamMembers?.length > 0 && (
          <>
            <GridRow className="tab-container">
              {pageData?.myTeam?.myTeamTabList?.map((myTeamTab, index) => {
                const totalTabContacts =
                  activeTab === myTeamTab?.tabName
                    ? `${filteredTeamMembers?.length}`
                    : `${myTeamData?.team?.members?.length - filteredTeamMembers?.length}`;

                return (
                  <GeTabButton
                    key={index}
                    index={index}
                    active={activeTab === myTeamTab.tabName}
                    tabChangeHandler={() => {
                      setActiveTab(myTeamTab.tabName);
                    }}
                    activeTab={false}
                    tabTitle={`${myTeamTab.tabName} (${totalTabContacts || 0})`}
                    tabData={undefined}
                    tooltipIcon={'ico-3dcube-16'}
                    tooltipId={''}
                  />
                );
              })}
            </GridRow>
            <GridRow className="cardInfo">
              {filteredTeamMembers?.map((member: any) => (
                <GridCell className="cardInfo__userInfo" desktop={4}>
                  {member.photoUrl ? (
                    <img className="cardInfo__userInfo_avatarIcon" src={member.photoUrl} alt={member.name} />
                  ) : (
                    <EDSIcon className="cardInfo__userInfo_avatarIcon" icon={'ico-placeholderavatar-32' as Icon} />
                  )}
                  <div className="cardInfo__container">
                    <div className="cardInfo__userDetails">
                      <div className="cardInfo__userDetails_userName">{member.name}</div>
                      <div className="cardInfo__userDetails_role">{member?.anaplanRole || member?.role}</div>
                    </div>
                    <div
                      {...prepareDataAnalyticsAttributes([
                        { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                        { name: 'linkType', value: pageData?.myTeam.contactLabel },
                        { name: 'linkName', value: `${pageData?.myTeam.contactLabel} ${member?.name}` },
                        { name: 'linkTitle', value: 'My Teams' },
                      ])}
                      className="cardInfo__link"
                      onClick={() => handleOpenPopup(member)}
                    >
                      {pageData?.myTeam.contactLabel}
                    </div>
                  </div>
                </GridCell>
              ))}
            </GridRow>
          </>
        )}

        {!isLoading && (pageDataLoading || filteredTeamMembers?.length > 0) && (
          <GridRow className="contact">
            <GridCell desktop={12} tablet={12} phone={12} className="contact__cell">
              <div className={'contact__cell_title'}>{pageData?.myTeam.title}</div>
              <div className={'contact__cell_description'}>{pageData?.myTeam.description}</div>
              <a
                href={pageData?.myTeam?.link?.value?.href}
                {...prepareDataAnalyticsAttributes([
                  { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                  { name: 'linkName', value: pageData?.myTeam.link.value.text },
                  { name: 'linkType', value: pageData?.myTeam.link.value.text },
                ])}
              >
                {pageData?.myTeam.link.value.text}
              </a>
            </GridCell>
          </GridRow>
        )}
        {isOpenPopup && (
          <ContactPopupComponent
            user={teamValue}
            pageData={pageData}
            setIsOpenPopup={setIsOpenPopup}
            country={country}
          />
        )}

        {!isLoading && !myTeamError && !userInfoError && !filteredTeamMembers?.length && (
          <NoResults pageData={pageData} />
        )}

        {(myTeamError || userInfoError) && <ServiceUnavailabilityView pageData={pageData} />}
      </GridContainer>
    </>
  );
};

export default MyTeams;
