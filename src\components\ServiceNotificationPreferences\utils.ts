import { SFDCNotificationFlagsLocalizationSettingDto } from '../../types/baseApi.types';
import { SelectedGroupItemList, NotificationType } from '../ServiceNotificationPreferences/models';

export const getLocalizedSFDCNotificationFlag = (sfdcNotificationsFlagsLocalization: any, code: string) => {
  return Object.keys(sfdcNotificationsFlagsLocalization)
    .map((key) => sfdcNotificationsFlagsLocalization[key as keyof SFDCNotificationFlagsLocalizationSettingDto])
    .find((group) => group?.code === code);
};

/** this logic will split the records which has channel value as "in-app;email" into two separate records  */
export const assignNotificationTypeList = (nSelectedItem: SelectedGroupItemList, notificationChannel: string) => {
  const notificationTypes: NotificationType[] = [];

  if (nSelectedItem.channelType) {
    const channels = nSelectedItem.channelType.split(';');

    channels.forEach((channel) => {
      if (channel.trim().length > 0 && channel.toLowerCase().match(notificationChannel.toLowerCase())) {
        notificationTypes.push({
          appName: nSelectedItem.appName,
          notificationChannel: channel,
          notificationCode: nSelectedItem.code,
          preferred: nSelectedItem.isChecked,
        });
      } else {
        if (channel.toLowerCase().match(notificationChannel.toLowerCase())) {
          notificationTypes.push({
            appName: nSelectedItem.appName,
            notificationChannel: channel,
            notificationCode: nSelectedItem.code,
            preferred: false,
          });
        }
      }
    });
  }
  return notificationTypes;
};

export const uniqueValues = (notificationTypeList: NotificationType[]) => {
  const rearrangeArrayList = notificationTypeList
    .filter((x) => x.preferred === false)
    .concat(notificationTypeList.filter((x) => x.preferred === true));

  notificationTypeList = Array.from(new Set(rearrangeArrayList.map((e) => JSON.stringify(e)))).map((e) =>
    JSON.parse(e),
  );

  return notificationTypeList;
};
