import { render, screen } from '@testing-library/react';
import { EquipmentStatusMessage, EquipmentStatusMessageInterface } from '../Component';

const props: EquipmentStatusMessageInterface = {
  pendingLabel: 'Pending',
  rejectedLabel: 'Rejected',
  status: 'Pending'
};
describe('EquipmentStatusMessage Component', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders pending state', () => {
    render(<EquipmentStatusMessage {...props} status='Pending' />);
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByTestId('equipment-status-pending')).toBeInTheDocument();
  });

  it('renders rejected state', () => {
    render(<EquipmentStatusMessage {...props} status='Rejected' />);
    expect(screen.getByText('Rejected')).toBeInTheDocument();
    expect(screen.getByTestId('equipment-status-rejected')).toBeInTheDocument();
  });

  it('renders in progress state', () => {
    render(<EquipmentStatusMessage {...props} status='In Progress' />);
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByTestId('equipment-status-pending')).toBeInTheDocument();
  });

  it('renders closed state (no message)', () => {
    render(<EquipmentStatusMessage {...props} status='Closed' />);
    expect(screen.queryByTestId('equipment-status-pending')).not.toBeInTheDocument();
    expect(screen.queryByTestId('equipment-status-rejected')).not.toBeInTheDocument();
  });

  it('renders undefined status (no message)', () => {
    render(<EquipmentStatusMessage {...props} status={undefined} />);
    expect(screen.queryByTestId('equipment-status-pending')).not.toBeInTheDocument();
    expect(screen.queryByTestId('equipment-status-rejected')).not.toBeInTheDocument();
  });

  it('does not render pending message when pendingLabel is empty', () => {
    render(<EquipmentStatusMessage {...props} pendingLabel="" status='Pending' />);
    expect(screen.queryByTestId('equipment-status-pending')).not.toBeInTheDocument();
  });

  it('does not render pending message when pendingLabel is undefined', () => {
    render(<EquipmentStatusMessage {...props} pendingLabel={undefined} status='Pending' />);
    expect(screen.queryByTestId('equipment-status-pending')).not.toBeInTheDocument();
  });

  it('does not render rejected message when rejectedLabel is empty', () => {
    render(<EquipmentStatusMessage {...props} rejectedLabel="" status='Rejected' />);
    expect(screen.queryByTestId('equipment-status-rejected')).not.toBeInTheDocument();
  });

  it('does not render rejected message when rejectedLabel is undefined', () => {
    render(<EquipmentStatusMessage {...props} rejectedLabel={undefined} status='Rejected' />);
    expect(screen.queryByTestId('equipment-status-rejected')).not.toBeInTheDocument();
  });

  it('applies error class for rejected status', () => {
    render(<EquipmentStatusMessage {...props} status='Rejected' />);
    const rejectedElement = screen.getByTestId('equipment-status-rejected');
    expect(rejectedElement).toHaveClass('equipment-status-message__wrap-error');
  });

  it('parses HTML content in pendingLabel', () => {
    const htmlLabel = '<strong>Pending</strong> registration';
    render(<EquipmentStatusMessage {...props} pendingLabel={htmlLabel} status='Pending' />);
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('registration')).toBeInTheDocument();
  });

  it('parses HTML content in rejectedLabel', () => {
    const htmlLabel = '<strong>Rejected</strong> registration';
    render(<EquipmentStatusMessage {...props} rejectedLabel={htmlLabel} status='Rejected' />);
    expect(screen.getByText('Rejected')).toBeInTheDocument();
    expect(screen.getByText('registration')).toBeInTheDocument();
  });

  it('renders correct icon for pending status', () => {
    render(<EquipmentStatusMessage {...props} status='Pending' />);
    const iconElement = screen.getByTestId('equipment-status-pending').querySelector('.equipment-status-message__icon');
    expect(iconElement).toBeInTheDocument();
  });

  it('renders correct icon for rejected status', () => {
    render(<EquipmentStatusMessage {...props} status='Rejected' />);
    const iconElement = screen.getByTestId('equipment-status-rejected').querySelector('.equipment-status-message__icon');
    expect(iconElement).toBeInTheDocument();
  });

  it('applies correct CSS classes for pending status', () => {
    render(<EquipmentStatusMessage {...props} status='Pending' />);
    const pendingElement = screen.getByTestId('equipment-status-pending');
    expect(pendingElement).toHaveClass('equipment-status-message__wrap');
    expect(pendingElement).not.toHaveClass('equipment-status-message__wrap-error');
  });

  it('applies correct CSS classes for rejected status', () => {
    render(<EquipmentStatusMessage {...props} status='Rejected' />);
    const rejectedElement = screen.getByTestId('equipment-status-rejected');
    expect(rejectedElement).toHaveClass('equipment-status-message__wrap');
    expect(rejectedElement).toHaveClass('equipment-status-message__wrap-error');
  });
});
