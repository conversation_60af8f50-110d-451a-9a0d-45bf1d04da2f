import { useMutation } from '@apollo/client';
import { OMNITURE_EVENTS, prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import { ErrorMessage, GeButton, GridCell, GridRow, RichText, Snackbar, Text } from 'cx-dle-component-library';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { isCSMAdminMode, isCSMMode } from '../../utils';
import { ToggleSwitch } from '../Common/GeToggleSwitch/Component';
import { GroupCheckList, NotificationType } from '../ServiceNotificationPreferences/models';
import { ServiceNotificationsGroupList } from '../ServiceNotificationsGroupList/Component';
import { ServiceNotificationsGroupControlProps } from './models';
import './styles.scss';
import { updateNotificationPreferenceMutation } from './mutation';
import { useNotificationsPreferences } from '../../context/NotificationsPreferencesContext';
import { assignNotificationTypeList, uniqueValues } from '../ServiceNotificationPreferences/utils';
import { SfdcNotificationsFlagsLocalizationType } from '../../models/Sitecommoncontent';

let preferenceSaveInprogress = false;

export const ServiceNotificationsGroup = (props: ServiceNotificationsGroupControlProps) => {
  const {
    settings,
    onChange,
    saveButtonText,
    discardButtonText,
    onSave,
    onError,
    notificationChannel,
    completeList: completeListItems,
    categoryDefinition,
    sfdcNotificationsFlagsLocalization,
  } = props;

  const [saveButtonDisabled, setSaveButtonEnabled] = useState(true);
  const [showSpinner, setShowSpinner] = useState(false);
  const [showCSMErrorBar, setShowCSMErrorBar] = useState(false);
  const { t } = useTranslation();

  // context
  const { updateNotificationPreferences, preferences, setSelectedPreferences } = useNotificationsPreferences();

  const toggleClass = (disableToggle: boolean) => {
    return disableToggle ? 'service-notification-group__hide' : 'ge-toggle-switch';
  };
  const linkTitle = `${notificationChannel}-preferences`;

  /** Logic to construct array of all the checked items for saving in the store */
  const reconstructArrayforStore = (
    completeList: NotificationType[],
    notificationTypeList: NotificationType[],
    notificationChannel: string,
  ) => {
    const otherChannelData: NotificationType[] = [];
    const intersection: NotificationType[] = [];

    const regex = new RegExp(notificationChannel.toLowerCase(), 'g');

    // get all the checked items from other channel

    completeList.forEach((y) => {
      if (y.notificationChannel.toLowerCase() !== notificationChannel.toLowerCase()) {
        intersection.push(y);
      }
    });

    intersection.forEach((m) => {
      if (
        m.notificationChannel &&
        m.notificationChannel.trim().split(';').length === 1 &&
        m.notificationChannel.trim().length > 0
      ) {
        otherChannelData.push({
          appName: m.appName,
          notificationChannel: m.notificationChannel,
          notificationCode: m.notificationCode,
          preferred: m.preferred,
        });
      }
    });

    intersection.forEach((m) => {
      if (
        m.notificationChannel &&
        m.notificationChannel.trim().split(';').length > 1 &&
        m.notificationChannel.trim().length > 1
      ) {
        otherChannelData.push({
          appName: m.appName,
          notificationChannel: m.notificationChannel.toLowerCase().replace(regex, ''),
          notificationCode: m.notificationCode,
          preferred: m.preferred,
        });
      }
    });

    let notificationCompleteList = otherChannelData.concat(notificationTypeList);
    notificationCompleteList = uniqueValues(notificationCompleteList);

    return notificationCompleteList;
  };

  const resetSelectedValues = (
    settings: GroupCheckList[],
    notificationType: NotificationType[],
    notificationChannel: string,
  ) => {
    notificationType = notificationType.filter((x) => x.notificationChannel.trim().length !== 0);

    settings.forEach((nData) => {
      nData.selectedValues.forEach((nSelected) => {
        const selectedNotificationType = notificationType.find(
          (m) =>
            m.notificationCode === nSelected.code &&
            m.notificationChannel.toLowerCase().match(notificationChannel.toLowerCase()),
        );
        nSelected.isChecked = selectedNotificationType !== undefined ? selectedNotificationType.preferred : false;
        nSelected.channelType =
          selectedNotificationType !== undefined ? selectedNotificationType.notificationChannel : nSelected.channelType;
      });
    });

    return settings;
  };

  const enableDisableSaveButton = () => {
    const enable = saveButtonDisabled;

    // enable: true && preferenceSaveInprogress : false
    if (enable) {
      setSaveButtonEnabled(!enable);
    } else if (!preferenceSaveInprogress && !enable) {
      //// enable: false && preferenceSaveInprogress = true
      const x = document.getElementsByClassName('ge-button--solid-primary');
      // tslint:disable-next-line: prefer-for-of
      for (let i = 0; i < x.length; i++) {
        const saveBtnElement: any = x[i];
        const saveBtnId = saveButtonText + '-' + notificationChannel;
        if (saveBtnElement && saveBtnElement.id === saveBtnId && saveBtnElement.disabled) {
          saveBtnElement.disabled = false;
        }
      }
      setSaveButtonEnabled(enable);
    }
  };

  const disableOtherSaveBtns = () => {
    const x = document.getElementsByClassName('btn-grp-right');
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < x.length; i++) {
      const saveBtnElement: any = x[i].getElementsByClassName('ge-button--solid-primary').item(0);
      const saveBtnId = saveButtonText + '-' + notificationChannel;
      if (saveBtnElement && saveBtnElement.id !== saveBtnId) {
        preferenceSaveInprogress = true;
        saveBtnElement.disabled = true;
      }
    }
  };

  let completeList = completeListItems;
  let notificationTypeList: NotificationType[] = [];

  settings.forEach((nData) => {
    nData.selectedValues.forEach((nSelected) => {
      const retValues = assignNotificationTypeList(nSelected, notificationChannel as string);
      notificationTypeList = notificationTypeList.concat(retValues);
    });
  });
  if (preferences.length > 0) {
    completeList = preferences.filter((m) => m.preferred !== false);
  }
  notificationTypeList = uniqueValues(notificationTypeList);

  const [updatePreferences] = useMutation(updateNotificationPreferenceMutation);

  console.log(props);

  return (
    <>
      <div>
        {showCSMErrorBar && (
          <Snackbar
            showCloseButton
            type="danger"
            onCloseClick={() => {
              setShowCSMErrorBar(false);
            }}
          >
            <ErrorMessage message={t('CSM.UserFriendlyMessage')} />
          </Snackbar>
        )}
      </div>

      <>
        <GridRow>
          <GridCell desktop={12} tablet={8} phone={4} className={'inner-row'}>
            <div className={settings.length > 0 ? 'btn-grp-right' : 'btn-grp-hide'} id="btn-group">
              <GeButton
                type="submit"
                className={'action-button'}
                onClick={() => {
                  const resetData = resetSelectedValues(settings, completeList, notificationChannel as string);
                  onChange(resetData);
                }}
                btnSize="medium"
                btnStyleType="stroked-secondary"
                {...prepareDataAnalyticsAttributes([
                  { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                  { name: 'linkName', value: discardButtonText },
                  { name: 'linkType', value: 'UpdateNotificationTypePreferences' },
                  { name: 'linkTitle', value: linkTitle },
                ])}
                btnLabel={discardButtonText}
              />
              <GeButton
                type="submit"
                className={'action-button'}
                disabled={saveButtonDisabled}
                showLoadingSpinner={showSpinner}
                onClick={() => {
                  if (isCSMMode() && !isCSMAdminMode()) {
                    setShowCSMErrorBar(true);
                    return;
                  }
                  setShowSpinner(!showSpinner);
                  disableOtherSaveBtns();
                  setSaveButtonEnabled(!saveButtonDisabled);
                  const notificationCompleteList = reconstructArrayforStore(
                    completeList,
                    notificationTypeList,
                    notificationChannel as string,
                  );
                  updateNotificationPreferences(notificationCompleteList);
                  setSelectedPreferences(
                    notificationCompleteList,
                    notificationChannel as string,
                    sfdcNotificationsFlagsLocalization as SfdcNotificationsFlagsLocalizationType,
                  );

                  onChange(settings);
                  updatePreferences({
                    variables: {
                      input: {
                        notificationType: notificationTypeList.map((m) => ({
                          appName: m.appName,
                          notificationChannel: m.notificationChannel.toLowerCase(),
                          notificationCode: m.notificationCode,
                          preferred: m.preferred,
                        })),
                      },
                    },
                    onCompleted: (data) => {
                      preferenceSaveInprogress = false;
                      onSave(true);
                      if (data.updateUserPreferences.notificationType && !saveButtonDisabled) {
                        setSaveButtonEnabled(!saveButtonDisabled);
                      }
                      setShowSpinner(false);
                    },
                    onError: (error) => {
                      setShowSpinner(!showSpinner);
                      onError(error);
                    },
                  });
                }}
                {...prepareDataAnalyticsAttributes([
                  { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                  { name: 'linkName', value: saveButtonText },
                  { name: 'linkType', value: 'UpdateNotificationTypePreferences' },
                  { name: 'linkTitle', value: linkTitle },
                ])}
                btnSize="medium"
                btnStyleType="solid-primary"
                btnLabel={saveButtonText}
              />
            </div>
          </GridCell>
        </GridRow>
      </>
      {settings.map((data: GroupCheckList, key) => {
        const { groupKey, offLabelText, onLabelText, selectedValues, disableToggle } = data;
        const filterItems = selectedValues.filter(({ groupName }) => groupName === groupKey).pop();
        const checkedState = selectedValues.some(
          (m) => m.isChecked && m.channelType.toLowerCase().match((notificationChannel as string).toLowerCase()),
        );
        return (
          <div
            className={filterItems?.hasAccess ? 'service-notification-group' : 'service-notification-group__hide'}
            key={key}
          >
            <GridRow>
              <GridCell desktop={6} className={'inner-row'}>
                <Text tag="h5" className="service-notification-group__title" text={groupKey} />
                <ToggleSwitch
                  groupKey={groupKey}
                  id={`${key}-${filterItems?.groupName}-${notificationChannel}`}
                  checked={checkedState}
                  analyticsAttributes={prepareDataAnalyticsAttributes([
                    { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                    { name: 'linkName', value: !checkedState ? 'toggle-on' : 'toggle-off' },
                    { name: 'linkType', value: 'ToggleSwitch' },
                    { name: 'linkTitle', value: `${notificationChannel}-${groupKey}` },
                  ])}
                  className={toggleClass(disableToggle)}
                  changeHandler={(e) => {
                    selectedValues.forEach((item) => {
                      item.channelType = (notificationChannel as string).toLocaleLowerCase();
                      item.isChecked = e.target.checked;
                    });

                    enableDisableSaveButton();
                    onChange(settings);
                  }}
                >
                  <div className="ge-track-all__label">
                    {selectedValues.some((m) => m.isChecked) ? onLabelText : offLabelText}
                  </div>
                </ToggleSwitch>
              </GridCell>
            </GridRow>
            <GridRow>
              <GridCell desktop={6} className={'inner-row horizontal-line'}>
                <RichText tag="div" className="ge-user-preferences__info-names" text={categoryDefinition[key]} />
              </GridCell>
            </GridRow>
            <GridRow>
              <GridCell desktop={12} className={'inner-row'}>
                <ServiceNotificationsGroupList
                  key={`service-notification-group-List__item-${key}`}
                  groupTitle={groupKey.toString()}
                  settings={selectedValues}
                  channelName={notificationChannel as string}
                  onChange={(code: string, checked: boolean) => {
                    selectedValues.forEach((item) => {
                      if (item.code === code) {
                        item.channelType = (notificationChannel as string).toLocaleLowerCase();
                        item.isChecked = checked;
                      }
                    });
                    enableDisableSaveButton();
                    onChange(settings);
                  }}
                />
              </GridCell>
            </GridRow>
          </div>
        );
      })}
    </>
  );
};
