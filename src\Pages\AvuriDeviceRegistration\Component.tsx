import React from 'react';

import { usePageData } from '../../hooks/common';
import { GridCell, GridContainer } from 'cx-dle-component-library';
import { RichText, Text } from 'cx-dle-component-library';

import { AvuriDeviceRegistration as AvuriDeviceRegistrationPops } from '../../models/AvuriDeviceRegistration';

import './styles.scss';
import { RegistrationFormControl } from '../../components';
import { Metadata } from '../../components/Metadata';
import BreadcrumbControl from '../../components/BreadcrumbNavigationNew/Component';

const AvuriDeviceRegistration = () => {
  const { data: pageData, isLoading: pageDataLoading } = usePageData<AvuriDeviceRegistrationPops>('avuriDeviceRegistration', 'avuriRegistrationData');
  if (pageDataLoading) {
    return null;
  }

  return (
    <>
      {pageData?.metadata && <Metadata metadata={pageData.metadata} />}
       <BreadcrumbControl
          breadcrumbLink={pageData?.breadcrumb.breadcrumbLink.href ?? ''}
          breadcrumbNavigationTitle={pageData?.breadcrumb.breadcrumbLink.text ?? ''} 
          breadcrumbPageTitle={pageData?.breadcrumb.breadcrumbPageTitle ?? ''}>
      </BreadcrumbControl>
      <div className="ge-heading-content__holder">
        <div className="ge-heading-content__main-content">
          <GridContainer>
            <Text
              key={pageData?.headline}
              text={pageData?.headline}
              className="ge-heading-content__title"
              tag="h2"
            />
            <GridCell desktop={12} tablet={8} phone={4}>
              <RichText text={pageData?.mainContent} />
            </GridCell>
          </GridContainer>
        </div>
      </div>
      <section className="ge-registration-content">
        <div className="ge-registration-form-columns">
          <div className="ge-two-column-container">
            <GridContainer>
              <RegistrationFormControl alertCloseIcon={pageData?.alertCloseIcon as string} />
            </GridContainer>
          </div>
        </div>
      </section>
    </>
  );
};

export default AvuriDeviceRegistration;
