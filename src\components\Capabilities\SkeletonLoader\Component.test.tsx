import React from 'react';
import { render, screen } from '@testing-library/react';
import { CapabilitiesAlertContainer, TitleLoaderSection } from './Component';

describe('CapabilitiesAlertContainer', () => {
  it('renders icon, title, and description', () => {
    render(
      <CapabilitiesAlertContainer
        icon="test-icon"
        title="Test Title"
        description="<p>Test Description</p>"
      />
    );
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    // Icon and RichText are from external libraries, so we just check for their containers
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });
});

describe('TitleLoaderSection', () => {
  it('renders loader wrappers', () => {
    render(<TitleLoaderSection />);
    // Check for loader-wrapper div
    expect(screen.getByClass('loader-wrapper')).toBeTruthy();
    // Check for at least one SkeletonLoader
    const skeletons = document.querySelectorAll('.capabilities-loader, .card-user-details__loading');
    expect(skeletons.length).toBeGreaterThan(0);
  });
});

// Helper for getByClass since RTL does not provide it directly
expect.extend({
  toBeTruthy(received) {
    return {
      pass: !!received,
      message: () => `expected ${received} to be truthy`,
    };
  },
  getByClass(received, className) {
    const el = document.querySelector(`.${className}`);
    return {
      pass: !!el,
      message: () => `expected to find element with class ${className}`,
    };
  },
});
