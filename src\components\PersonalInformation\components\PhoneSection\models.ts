export interface PhoneSectionJssProps {
  closeButtonText: string;
  editButtonText: string;
  saveButtonText: string;
  cancelButtonText: string;
  requiredLabelText: string;
  updateConfirmationMessage: string;
  phoneLabelText: string;
  phoneSectionDescription: string;
  newPhoneLabelText: string;
  newPhonePlaceholderText: string;
  newPhoneHelperText: string;
  emptyNewPhoneErrorMessage: string;
}

export interface PhoneSectionProps extends PhoneSectionJssProps {
  phone: string;
  siteName: string;
  phoneExtension?: string | undefined;
  refetch: () => void;
  extension: ExtensionProps;
  formName: string;
  subFormName: string;
  filteredCountryList: CountryCodeDropDowType[];
  countryCode: string;
}

export interface CountryCodeDropDowType {
  name: string;
  maxLength: string;
  countryPhoneNumberPlaceholder: string;
  flagSource: string;
  validationMessage: string;
  minLength: string;
  countryCode: string;
  countryISDCode: string;
  countryName: string;
}

export interface ExtensionProps {
  phoneExtensionLabelText: string;
  phoneExtensionOptionLabel: string;
  validations: ValidationProps[];
}

export interface ValidationProps {
  maxCharacters: number;
  regex: RegExp;
  supportedCountries: string[];
  validCode: string[];
}
