{"name": "cx-account-app", "version": "1.5.0", "private": true, "workspaces": ["libs/npm/@gehc-ux/*"], "bin": {"cross-env": "./node_modules/cross-env/src/bin/cross-env.js"}, "dependencies": {"@apollo/client": "^3.12.6", "@babel/runtime": "^7.25.7", "@tanstack/react-query": "^5.56.2", "@types/lodash": "^4.17.13", "axios": "^1.7.7", "buffer": "^6.0.3", "cx-dle-common-lib": "1.6.0-develop.42", "cx-dle-component-library": "1.6.0-develop.193", "html-react-parser": "^5.1.18", "i18next": "^23.16.2", "i18next-http-backend": "^2.6.2", "jwt-decode": "^4.0.0", "launchdarkly-react-client-sdk": "^3.1.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-cookie": "^7.2.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.1.2", "react-i18next": "^15.1.0", "react-router-dom": "^7.1.3", "react-scripts": "5.0.1", "react-tooltip": "^4.2.10", "sass": "^1.79.1", "web-vitals": "^2.1.4"}, "scripts": {"startdepricated": "react-scripts start", "start": "SET NODE_OPTIONS=--openssl-legacy-provider && node ./scripts/webpack/scripts/start.js", "build": "cross-env-shell NODE_OPTIONS=--openssl-legacy-provider \"node ./scripts/webpack/scripts/build.js\"", "test": "jest --watch --no-cache --config ./scripts/jest/jest.config.ts || exit 0", "test:watch": "jest --watch --notify --config ./scripts/jest/jest.config.ts", "test:coverage": "jest --coverage --config ./scripts/jest/jest.config.ts", "test:ci": "set CI=true && jest --ci --coverage --config ./scripts/jest/jest.config.ts --testResultsProcessor ./node_modules/jest-sonar-reporter/index.js", "test:snapshot": "jest --updateSnapshot --no-cache --config ./scripts/jest/jest.config.ts", "test:snapshot:watch": "jest --watch --updateSnapshot --config ./scripts/jest/jest.config.ts", "eject": "react-scripts eject", "generate-models": "ts-node -P tsconfig.scripts.json scripts/generateModels.ts"}, "engines": {"node": "^20.17.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-modules-commonjs": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.7", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@loadable/babel-plugin": "^5.15.3", "@loadable/webpack-plugin": "^5.15.2", "@tanstack/react-query-devtools": "^5.59.20", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/classnames": "^2.3.1", "@types/jest": "^27.5.2", "@types/jsdom": "^21.1.6", "@types/node": "^16.18.123", "@types/node-fetch": "^2.6.11", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@types/react-helmet": "^6.1.11", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "assert": "^2.1.0", "autoprefixer": "^10.4.18", "babel-loader": "^9.2.1", "babel-preset-react-app": "^10.0.1", "browserify-fs": "^1.0.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chalk": "^4.1.2", "cheerio": "1.0.0-rc.12", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.0", "css-loader": "^6.10.0", "css-minimizer-webpack-plugin": "^5.0.1", "entities": "^4.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.34.1", "eslint-webpack-plugin": "^4.2.0", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "fs-extra": "^11.2.0", "hostile": "^1.3.3", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.6.0", "https-browserify": "^1.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-serializer-html": "^7.1.0", "jest-sonar-reporter": "^2.0.0", "jsdom": "^24.0.0", "mini-css-extract-plugin": "^2.8.1", "moment": "^2.29.4", "node-fetch": "^3.3.2", "npm-run-all": "^4.1.5", "os-browserify": "^0.3.0", "postcss": "^8.4.35", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^15.1.0", "postcss-loader": "^7.3.4", "prettier": "^2.8.8", "process": "^0.11.10", "react-dev-utils": "^12.0.1", "resolve-url-loader": "^5.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^4.9.5", "url": "^0.11.4", "url-loader": "^4.1.1", "util": "^0.12.5", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-manifest-plugin": "^5.0.0", "webpack-visualizer-plugin": "^0.1.11", "yargs": "^17.7.2"}}