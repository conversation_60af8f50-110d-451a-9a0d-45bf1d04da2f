// This file is auto-generated. Do not edit manually.

export interface Myteam {
  pageTitle: string;
  breadcrumbProductName: string;
  breadcrumbBackIcon: string;
  breadcrumbSeparator: string;
  breadcrumbBackRedirectUrl: string;
  breadcrumbBackRedirectText: string;
  breadcrumb: BreadcrumbType;
  teamMember: string;
  metadata: MetadataType;
  myTeam: MyTeamType;
  myTeamContact: MyTeamContactType;
}

export interface BreadcrumbType {
  isBreadcrumbEnabled: boolean;
  breadcrumbPageTitle: string;
  breadcrumbLink: BreadcrumbLinkType;
}

export interface BreadcrumbLinkType {
  href: string;
  text: string;
}

export interface MetadataType {
  browserTitle: string;
  metaAdditionalTags: MetaAdditionalTagType[];
  openGraphTags: OpenGraphTagType[];
  httpEquivalentTags: HttpEquivalentTagType[];
  linkTags: LinkTagType[];
}

export interface MetaAdditionalTagType {
  property: string;
  content: string;
}

export interface OpenGraphTagType {
  property: string;
  content: string;
}

export interface HttpEquivalentTagType {
  property: string;
  content: string;
}

export interface LinkTagType {
  rel: string;
  href: string;
  as: string;
}

export interface MyTeamType {
  contactLabel: string;
  description: string;
  link: LinkType;
  roleList: any[];
  tabList: string;
  title: string;
  myTeamTabList: MyTeamTabLisType[];
  userLocale: UserLocalType[];
  noTeamMemberDescription: string;
  noTeamMemberImage: NoTeamMemberImageType;
  noTeamMemberLink: NoTeamMemberLinkType;
  noTeamMemberTitle: string;
  serviceUnavailabilityMessage: string;
  serviceUnavailabilityLink: ServiceUnavailabilityLinkType;
}

export interface LinkType {
  value: ValueType;
}

export interface ValueType {
  href: string;
  text: string;
  linktype: string;
  url: string;
  anchor: string;
  target: string;
}

export interface MyTeamTabLisType {
  name: string;
  tabName: string;
  role: RolType[];
}

export interface RolType {
  countryCode: string;
  label: string;
}

export interface UserLocalType {
  countryCode: string;
  languageLocale: string;
}

export interface NoTeamMemberImageType {
  value: ValueType;
}

export interface ValueType {
  src: string;
  alt: string;
}

export interface NoTeamMemberLinkType {
  value: ValueType;
}

export interface ValueType {
  href: string;
  text: string;
  linktype: string;
  url: string;
  anchor: string;
  target: string;
}

export interface ServiceUnavailabilityLinkType {
  value: ValueType;
}

export interface ValueType {
  href: string;
  text: string;
  linktype: string;
  url: string;
  anchor: string;
  target: string;
}

export interface MyTeamContactType {
  sendMessageLabel: string;
  helpLabel: string;
  attachementLabel: string;
  description: string;
  emailSubject: string;
  failureDescription: string;
  failureTitle: string;
  successDescription: string;
  successTitle: string;
  fileTypesAllowed: string;
  hideUploadSection: boolean;
  uploadTagline: string;
  allowedFileSize: string;
  fileSizeValidationMessage: string;
  fileTypeValidationMessage: string;
  helpRequiredLabel: string;
  helpValidationMessage: string;
  numberOfFilesUploadedValidationMessage: string;
  optionalLabel: string;
  tagline: string;
}