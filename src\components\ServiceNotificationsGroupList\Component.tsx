import { OMNITURE_EVENTS, prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import { RichText, Checkbox } from 'cx-dle-component-library';
import { ServiceNotificationsGroupItemProps } from './models';
import './styles.scss';

export const ServiceNotificationsGroupList: React.FC<ServiceNotificationsGroupItemProps> = ({
  onChange,
  settings,
  channelName,
}) => {
  const checkboxStatusValue = (isChecked: boolean, channelType: string) => {
    return !!(isChecked && channelType.toLowerCase().match(channelName.toLowerCase()));
  };

  const checkboxClassName = (disableSelection: boolean) => {
    return disableSelection
      ? 'service-notification-group-List__hide'
      : 'service-notification-group-List__item-checkbox';
  };

  const checkboxAnalyticLinkName = (isChecked: boolean) => {
    return isChecked ? 'checkbox-unchecked' : 'checkbox-checked';
  };

  return (
    <ul className="service-notification-group-List__list">
      {settings.map((preference, key) => {
        const linkTitle = `${channelName}-${preference.groupName}`;
        return (
          <li key={`${key}-${preference.code}-${channelName}`} className="service-notification-group-List__item">
            <Checkbox
              name={`${preference.code}-${channelName}`}
              className={checkboxClassName(preference.disableSelection)}
              value={checkboxStatusValue(preference.isChecked, preference.channelType)}
              changeHandler={(event) => {
                onChange(preference.code, event.target.checked);
              }}
              {...prepareDataAnalyticsAttributes([
                { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                { name: 'linkName', value: checkboxAnalyticLinkName(preference.isChecked) },
                { name: 'linkType', value: preference.name },
                { name: 'linkTitle', value: linkTitle },
              ])}
            >
              <div className="service-notification-group-List__label">
                <RichText tag="div" className="service-notification-group-List__label-title" text={preference.title} />
              </div>
            </Checkbox>
            <RichText
              tag="div"
              className="service-notification-group-List__text"
              text={
                preference.description === ''
                  ? preference.richDescription
                  : channelName.toLowerCase() === 'email'
                  ? preference.emailPreferenceDescription
                  : preference.description
              }
            />
          </li>
        );
      })}
    </ul>
  );
};
