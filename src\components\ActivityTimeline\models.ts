import { LocaleDateTime } from "cx-dle-common-lib/Hooks/useLocaleMoment/models";
import { ModalityLocalizationType, ServiceEventTypeLocalizationType, SrStatusLocalizationType } from "../../models/Dlesitecommoncontent";
import { ActivityTimelineType, EngineerTypeCodesLocalizatioType, ServiceStateMessageCodesLocalizatioType } from "../../models/Notifications";
import { Dispatch, SetStateAction } from "react";

export interface DataExportSubscriptionEntity {
    jobId: string;
    documentUrl: string;
    isDownloadCompleted?: boolean;
  }
  export enum SubscriptionType {
    DOCUMENT_DOWNLOAD = 'Document Download',
    REPORTS = 'Reports',
  }

export interface TimelineLocalizationData {
  serviceStateMessageCodesLocalization: ServiceStateMessageCodesLocalizatioType[] | undefined;
  engineerTypeCodesLocalization: EngineerTypeCodesLocalizatioType[] | undefined;
  srStatusLocalization: SrStatusLocalizationType[] | undefined;
  modalityLocalization: ModalityLocalizationType[] | undefined;
  serviceEventTypeLocalization: ServiceEventTypeLocalizationType[] | undefined;
}
export interface ActivityConfig {
  isNewUXEquipementCard: boolean;
  isServiceEventV2QueryServiceTracker: boolean;
  enableFullAddressForLocation: boolean;
}

export interface ActivityTimelineWrapperProps extends ActivityTimelineProps {
  loadingToggle: boolean;
  downloadDocumentCollection: null;
  documentCollection: null;
  shouldShowAlert: boolean,
  setShouldShowAlert: Dispatch<SetStateAction<boolean>>;
  setLoadingToggle: Dispatch<SetStateAction<boolean>>;
  setDocumentCollection: Dispatch<SetStateAction<null>>;
  setDownloadDocumentCollection: Dispatch<SetStateAction<null>>;
  selectedItems: string[] | null;
  addSelectedItem: (item: string) => void;
  removeSelectedItem: (item: string) => void;
  resetSelectedItems: () => void;
  isSelectedItem: (item: string) => boolean;
}

export interface ActivityTimelineProps {
  handleCloseButton: () => void;
  seviceEventId: string;
  componentInternalError: string;
  timelineLocalizationData: TimelineLocalizationData;
  dateTimeUtils: LocaleDateTime;
  activityConfig: ActivityConfig;
  activityTimelineStaticData: ActivityTimelineType;
}

  

  export const DOCUMENT_HANDLER_TYPE = {
    direct: 'DIRECT',
    popup: 'POPUP',
  };
  