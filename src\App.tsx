import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Layout, Redirect } from './components';
import { LanguageProvider } from './context/LanguageContext';
import config from './config';
import { initI18n } from './i18n/config';
import Settings from './Pages/Settings';

import AvuriDeviceRegistration from './Pages/AvuriDeviceRegistration';
import Account from './Pages/Account';
import NotificationsList from './Pages/Notifications';
import { LDProvider } from 'launchdarkly-react-client-sdk';
import getUserDetailsFromCookies from './utils/helpers';
import MyTeams from './Pages/MyTeams';
import SetupAccount from './Pages/AccountSetup';
import { SubscriptionClientProvider } from './context/SubscriptionClientContext';
import LoginProblem from './Pages/loginProblem';
import { LOGIN_PROBLEM_COUNTRIES, currentDomain } from './constants';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      staleTime: Infinity,
    },
  },
});

const App: React.FC = () => {
  const [isI18nInitialized, setIsI18nInitialized] = useState(false);

  useEffect(() => {
    const init = async () => {
      try {
        await initI18n();
        setIsI18nInitialized(true);
      } catch (error) {
        console.error('Failed to initialize i18n:', error);
        setIsI18nInitialized(true);
      }
    };
    init();
  }, []);
  

  const userInfo = getUserDetailsFromCookies();
  const isRedirectToLoginProblem = LOGIN_PROBLEM_COUNTRIES.includes(userInfo?.address?.country);

  useEffect(() => {
    if (isRedirectToLoginProblem) {
      const redirectUrl = `${currentDomain}/${config?.loginProblemRoute}`;
      if (window.location.href !== redirectUrl) {
        window.location.href = redirectUrl;
      }
    }
  }, [isRedirectToLoginProblem]);

  const LDContext = {
    "kind": "user",
    "key": userInfo?.email,
    "name": userInfo?.email,
    "email": userInfo?.email
  };
  

  if (!isI18nInitialized) {
    return null; // Or a loading spinner
  }

  return (
    <LDProvider context={LDContext} clientSideID={config.launchDarklyClientSideId} reactOptions={{useCamelCaseFlagKeys:false}}>
      <QueryClientProvider client={queryClient}>
        <ReactQueryDevtools initialIsOpen={false} position="left" />
        <Router>
          <Routes>
            {/* This path will match URLs like /v1, /en-US/v1, /pt-BR/v1 */}
            <Route
              path="/:lang?/"
              element={
                <LanguageProvider>
                  <SubscriptionClientProvider> 
                    <Layout />
                  </SubscriptionClientProvider>
                </LanguageProvider>
              }
            >
              {/* Dynamic language route */}
              <Route path={config?.accountPageRoute} element={<Account />} />
              <Route path={config?.notificationPageRoute} element={<NotificationsList />} />
              <Route path={config?.settingsPageRoute} element={<Settings />}/>
              <Route path={config?.accountSetupPageRoute} element={<SetupAccount />} />
              <Route path={config?.avuriRegistrationPageRoute} element={<AvuriDeviceRegistration />} />
              <Route path={config?.accountMyTeamPageRoute} element={<MyTeams />} />    
              <Route path={config?.loginProblemRoute} element={<LoginProblem />} />           

              {/* Catch-all redirect 404 for unknown routes */}
              <Route path="*" element={<Redirect to={config?.errorPages?.error404} />} />
            </Route>
          </Routes>
        </Router>
      </QueryClientProvider>
    </LDProvider>
  );
};

export default App;
