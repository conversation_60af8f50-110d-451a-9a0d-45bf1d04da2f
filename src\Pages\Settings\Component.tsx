import { usePageData } from '../../hooks';
import { Settings as SettingsProps } from '../../models/Settings';
import PageTitle from '../../components/Common/PageTitle';
import TabContainer from '../../components/TabContainer';
import { Grid<PERSON>ell, GridContainer, GridRow } from 'cx-dle-component-library';
import { Metadata } from '../../components/Metadata';
import BreadcrumbControl from '../../components/BreadcrumbNavigationNew/Component';
export const Settings = () => {
  const { data, isLoading }:any = usePageData<SettingsProps>('settings', 'settingsData');
  if (isLoading) {
    return null;
  }

  return (
    <>
      {data?.metadata && <Metadata metadata={data.metadata} />}
      <BreadcrumbControl
        breadcrumbLink={data?.breadcrumb.breadcrumbLink.href ?? ''}
        breadcrumbNavigationTitle={data?.breadcrumb.breadcrumbLink.text ?? ''}
        breadcrumbPageTitle={data?.breadcrumb.breadcrumbPageTitle ?? ''}
      ></BreadcrumbControl>
      <GridContainer>
        <GridRow>
          <GridCell desktop={12} tablet={8} phone={4}>
            <PageTitle title={data?.pageTitle || ''} />
            <TabContainer
              tabs={data?.tabs}
              alertCloseIcon={data.alertCloseIcon}
              supportedCountriesForCommunications={data.supportedCountriesForCommunications}
            />
          </GridCell>
        </GridRow>
      </GridContainer>
    </>
  );
};
