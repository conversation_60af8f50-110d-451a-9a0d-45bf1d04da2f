
import { FormValues } from 'cx-dle-common-lib';

export interface CountryCodeDropDowType {
    name: string;
    maxLength: string;
    countryPhoneNumberPlaceholder: string;
    flagSource: string;
    validationMessage: string;
    minLength: string;
    countryCode: string;
    countryISDCode: string;
    countryName: string;
    isAvailableInPackage: boolean;
}
export interface AccountNumberPopupImageType {
    src: string;
    alt: string;
    width: string;
    height: string;
}
export interface IntroduceyourselfProps {
    introduceYourselfCaption: string;
    firstNameLabel: string;
    firstNameHelperTextLabel: string;
    firstNameOptionalRequiredLabel: string;
    firstNamePlaceholder: string;
    firstNameMaxCharacters: string;
    firstNameOptional: boolean;
    firstNameRequiredValidationMessage: string;
    firstNameRegEx: string;
    firstLocalNameLabel: string;
    lastNameMaxCharacters: string;
    localFirstNameValidation: string;
    firstNameLocalRegEx: string;
    lastNameLabel: string;
    lastNameHelperTextLabel: string;
    lastNameOptionalRequiredLabel: string;
    lastNamePlaceholder: string;
    lastNameOptional: boolean;
    lastNameRequiredValidationMessage: string;
    lastNameRegEx: string;
    lastLocalNameLabel: string;
    localLastNameRequired: string;
    localLastNameValidation: string;
    lastNameLocalRegEx: string;
    countryCodeDropDown: CountryCodeDropDowType[];
    phoneNumberOptional: boolean;
    businessPhoneNumberValidationMessage: string;
    phoneRequiredValidationMessage: string;
    phoneNumberOptionalRequiredLabel: string;
    phoneNumberLabel: string;
    phoneNumberMaxCharacters: string;
    phoneNumberPlaceholder: string;
    businessPhoneNumberLabel: string;
    phoneRegEx: string;
    phoneNumberHelperTextLabel: string;
    customerAccountNumberLabel: string;
    customerAccountNumberPlaceholder: string;
    customerAccountNumberMaxCharacters: string;
    accountNumberPopupTitle: string;
    accountNumberPopupImage: AccountNumberPopupImageType;
    accountNumberPopupText: string;
    customerAccountNumberTextLabel: string;
    cnpjCPFLabel: string;
    cnpjCPFTextLabel: string;
    cnpjCPFOptionalRequiredLabel: string;
    cnpjCPFPlaceholder: string;
    cnpjCPFMaxCharacters: string;
    cnpjCPFValidationMessage: string;
    ultrasoundSerialLabel: string;
    ultrasoundSerialPlaceholder: string;
    ultrasoundSerialMaxCharacters: string;
    ultrasoundSerialValidationMessage: string;
    ultrasoundPopupTitle: string;
    ultrasoundPopupText: string;
    ultrasoundSerialHintText: string;
    localFirstNameRequired: string;
    sourceApplication: string;
    defaultValues: FormValues;
    // TODO change this with proper type
    submitState: any;
    websiteCountryCode: string;
    globalCountry: string;
    handlePhoneNumberError: (isError: boolean) => void;
    preferredCountryCode: string;
    onCountryChange?: (countryCode: string) => void;
}