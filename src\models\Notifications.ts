// This file is auto-generated. Do not edit manually.

export interface Notifications {
  pageTitle: string;
  metadata: MetadataType;
  notifications: NotificationsType;
  activityTimeline: ActivityTimelineType;
}

export interface MetadataType {
  browserTitle: string;
  metaAdditionalTags: MetaAdditionalTagType[];
  openGraphTags: OpenGraphTagType[];
  httpEquivalentTags: HttpEquivalentTagType[];
  linkTags: LinkTagType[];
}

export interface MetaAdditionalTagType {
  property: string;
  content: string;
}

export interface OpenGraphTagType {
  property: string;
  content: string;
}

export interface HttpEquivalentTagType {
  property: string;
  content: string;
}

export interface LinkTagType {
  rel: string;
  href: string;
  as: string;
}

export interface NotificationsType {
  groupingRules: GroupingRuleType[];
  changePreferencesLink: ChangePreferencesLinkType;
  detailsButtonText: string;
  loadMoreButtonText: string;
  noNotificationsMessageText: string;
  notificationRefreshButtonText: string;
  notificationRefreshIcon: NotificationRefreshIconType;
  notificationContractExpiredText: string;
  overduePMMessageText: string;
  notificationWarrantyExpiredText: string;
  onHoldMessageText: string;
  onWatchMessageText: string;
  enableNotificationCardV2: boolean;
  numberOfCardsToRequestFirstTime: string;
  numberOfCardsToBeLoadedUsingLoadMoreButton: string;
  timeframeToSearchForNotifications: string;
  dateFormat: string;
  pageTitle: string;
  breadcrumbProductName: string;
  breadcrumbBackIcon: string;
  breadcrumbSeparator: string;
  breadcrumbBackRedirectUrl: string;
  breadcrumbBackRedirectText: string;
  breadcrumb: BreadcrumbType;
  onlyAllowVerifiedUsers: boolean;
  onlyAllowUnverifiedUsers: boolean;
  onlyAllowPeopleAdmin: boolean;
  serviceStateMessageCodesLocalization: ServiceStateMessageCodesLocalizatioType[];
  engineerTypeCodesLocalization: EngineerTypeCodesLocalizatioType[];
}

export interface GroupingRuleType {
  id: string;
  name: string;
  dateFormat: string;
  groupTitleTemplate: string;
  endOfTheNThDay: string;
  startOfTheNThDay: string;
}

export interface ChangePreferencesLinkType {
  href: string;
  text: string;
  linktype: string;
  url: string;
  anchor: string;
  target: string;
}

export interface NotificationRefreshIconType {
  url: string;
  fields: FieldsType;
}

export interface FieldsType {
  name: string;
  iconName: string;
  iconPrefix: string;
}

export interface BreadcrumbType {
  isBreadcrumbEnabled: boolean;
  breadcrumbPageTitle: string;
  breadcrumbLink: BreadcrumbLinkType;
}

export interface BreadcrumbLinkType {
  href: string;
  text: string;
}

export interface ServiceStateMessageCodesLocalizatioType {
  code: string;
  message: string;
  messagePattern: string;
}

export interface EngineerTypeCodesLocalizatioType {
  code: string;
  value: string;
}

export interface ActivityTimelineType {
  name: string;
  srNumberPrefix: string;
  activityTimestampDivider: string;
  equipmentIsDownStatusText: string;
  srStatusTimestampFormat: string;
  closeButtonText: string;
  activityTimestampDateFormat: string;
  activityTimestampTimeFormat: string;
  timeZoneNotificationLabel: string;
  activityTimelineTitle: string;
  plannedServiceTitle: string;
  overDueMessageText: string;
  isFromPMTracker: boolean;
  enableDeferredStatus: boolean;
  serviceOnHoldCardTitleText: string;
  serviceOnHoldMessageText: string;
  srActivityTypeCheck: string;
  srNoActivityText: string;
  serviceResolvedMessageText: string;
  enableMoreInfoCheck: boolean;
  srSourceTypeCheck: string;
  enablePartStatus: boolean;
  partStatuses: PartStatuseType[];
  hideDocumentLinkOfSRType: string;
  overDueServiceTitle: string;
  plannedServiceMessageText: string;
  serviceScheduledText: string;
  equipmentIsUnavailableStatusText: string;
  dateFormat: string;
  activityTimelineDeferredDateFormat: string;
  activityTimelineDueDateFormat: string;
  modelLabel: string;
  equipmentIsLimitedStatusText: string;
  numberBasedOnAssetLocation: string;
  includeChildAsset: boolean;
  sourceType: string;
  locale: string;
  enableWebsocketSubcription: boolean;
  downloadIcon: DownloadIconType;
  cancelButtonText: string;
  documentsLinkText: string;
  downloadButtonText: string;
  nonImagingLabel: string;
  showActivityDueDate: boolean;
  popoverHeader: string;
  enablePopover: boolean;
  plannedSRs: PlannedSRType[];
  unPlannedSRs: UnPlannedSRType[];
  downloadFileType: DownloadFileTypType[];
}

export interface PartStatuseType {
  name: string;
  statusDescription: string;
  statusTitle: string;
}

export interface DownloadIconType {
  iconName: string;
}

export interface PlannedSRType {
  code: string;
  title: string;
}

export interface UnPlannedSRType {
  code: string;
  title: string;
}

export interface DownloadFileTypType {
  value: string;
  code: string;
}