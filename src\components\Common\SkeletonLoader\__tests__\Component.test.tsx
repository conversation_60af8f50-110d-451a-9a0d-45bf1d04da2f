// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { SkeletonLoader } from '../Component';
import { SkeletonLoaderProps, SkeletonStyles } from '../models';

// Mock SCSS imports
jest.mock('../styles.scss', () => ({}));

describe('SkeletonLoader Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {

    it('renders without crashing with undefined list', () => {
      const props: SkeletonLoaderProps = {
        list: undefined as any
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      expect(container.firstChild).toBeNull();
    });

    it('renders skeleton wrapper with basic width and height', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px'
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrapper = container.querySelector('.skeleton-wrapper');
      
      expect(skeletonWrapper).toBeInTheDocument();
      expect(skeletonWrapper).toHaveStyle({
        width: '100px',
        height: '20px'
      });
    });

    it('applies custom className to skeleton wrapper', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px',
          className: 'custom-skeleton'
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrapper = container.querySelector('.skeleton-wrapper');
      
      expect(skeletonWrapper).toHaveClass('skeleton-wrapper');
      expect(skeletonWrapper).toHaveClass('custom-skeleton');
    });

    it('applies margin styles correctly', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px',
          margin: '10px 20px'
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrapper = container.querySelector('.skeleton-wrapper');
      
      expect(skeletonWrapper).toHaveStyle({
        margin: '10px 20px'
      });
    });
  });

  describe('Rows Rendering', () => {
    it('renders rows when rows array is provided', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          rows: [
            { width: '100px', height: '20px' },
            { width: '150px', height: '25px' }
          ]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const rows = container.querySelectorAll('div[style*="flex"]');
      
      expect(rows.length).toBeGreaterThan(0);
    });

    it('applies row styles correctly', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          rows: [{
            width: '200px',
            height: '30px',
            margin: '5px',
            className: 'row-skeleton'
          }]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const rowDiv = container.querySelector('div[style*="200px"]');
      
      expect(rowDiv).toBeInTheDocument();
      expect(rowDiv).toHaveStyle({
        width: '200px',
        height: '30px',
        margin: '5px'
      });
      expect(rowDiv).toHaveClass('row-skeleton');
    });

    it('sets flex direction to column when nested rows exist', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          rows: [{
            width: '200px',
            height: '30px',
            rows: [{ width: '100px', height: '15px' }]
          }]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const rowDiv = container.querySelector('div[style*="column"]');
      
      expect(rowDiv).toBeInTheDocument();
      expect(rowDiv).toHaveStyle({
        flexDirection: 'column'
      });
    });

    it('sets flex direction to row when no nested rows exist', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          rows: [{
            width: '200px',
            height: '30px'
          }]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const rowDiv = container.querySelector('div[style*="row"]');
      
      expect(rowDiv).toBeInTheDocument();
      expect(rowDiv).toHaveStyle({
        flexDirection: 'row'
      });
    });
  });

  describe('Columns Rendering', () => {
    it('renders columns when columns array is provided', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          columns: [
            { width: '100px', height: '20px' },
            { width: '150px', height: '25px' }
          ]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const columnContainer = container.querySelector('div[style*="column"]');
      
      expect(columnContainer).toBeInTheDocument();
      expect(columnContainer).toHaveStyle({
        display: 'flex',
        flexDirection: 'column'
      });
    });

    it('applies column wrapper styles correctly', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          columns: [{
            width: '200px',
            height: '30px',
            margin: '10px',
            className: 'column-skeleton'
          }]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const columnWrapper = container.querySelector('.skeleton-wrapper.column-skeleton');
      
      expect(columnWrapper).toBeInTheDocument();
      expect(columnWrapper).toHaveStyle({
        width: '200px',
        height: '30px',
        margin: '10px'
      });
    });
  });

  describe('Complex Nested Structures', () => {
    it('handles nested rows and columns', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          rows: [{
            columns: [
              { width: '100px', height: '20px' },
              { width: '150px', height: '25px' }
            ]
          }]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrappers = container.querySelectorAll('.skeleton-wrapper');
      
      expect(skeletonWrappers.length).toBeGreaterThan(0);
    });

    it('handles multiple list items', () => {
      const props: SkeletonLoaderProps = {
        list: [
          { width: '100px', height: '20px' },
          { width: '200px', height: '30px' },
          { width: '150px', height: '25px' }
        ]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrappers = container.querySelectorAll('.skeleton-wrapper');
      
      expect(skeletonWrappers).toHaveLength(3);
    });
  });

  describe('Edge Cases', () => {
    it('handles empty rows array', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          rows: []
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      expect(container.firstChild).toBeInTheDocument();
    });

    it('handles empty columns array', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          columns: []
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      expect(container.firstChild).toBeInTheDocument();
    });

    it('handles mixed content with rows, columns, and direct properties', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px',
          rows: [{ width: '50px', height: '10px' }],
          columns: [{ width: '75px', height: '15px' }]
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrappers = container.querySelectorAll('.skeleton-wrapper');
      
      expect(skeletonWrappers.length).toBeGreaterThan(0);
    });
  });

  describe('Styling', () => {
    it('applies default skeleton-wrapper class', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px'
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrapper = container.querySelector('.skeleton-wrapper');
      
      expect(skeletonWrapper).toHaveClass('skeleton-wrapper');
    });

    it('combines default class with custom className', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px',
          className: 'my-custom-class'
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrapper = container.querySelector('.skeleton-wrapper');
      
      expect(skeletonWrapper).toHaveClass('skeleton-wrapper');
      expect(skeletonWrapper).toHaveClass('my-custom-class');
    });

    it('handles undefined className gracefully', () => {
      const props: SkeletonLoaderProps = {
        list: [{
          width: '100px',
          height: '20px',
          className: undefined
        }]
      };
      
      const { container } = render(<SkeletonLoader {...props} />);
      const skeletonWrapper = container.querySelector('.skeleton-wrapper');
      
      expect(skeletonWrapper).toHaveClass('skeleton-wrapper');
      expect(skeletonWrapper.className).toBe('skeleton-wrapper ');
    });
  });
});
