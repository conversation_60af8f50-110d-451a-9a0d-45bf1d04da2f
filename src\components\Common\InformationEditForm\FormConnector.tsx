import React from 'react';
import { useFormContext, FieldValues } from 'react-hook-form';
import { FormConnectorProps } from './models';

export const mapConnectorProps = (context: ReturnType<typeof useFormContext>) => {
  const {
    formState: { dirtyFields, touchedFields, isValid, isSubmitting, errors },
    getValues,
    setValue,
    setError,
    reset,
    register,
    unregister,
    getFieldState,
    trigger,
  } = context;
  const values = getValues();
  const valid = Object.keys(values).reduce((init, key) => {
    const state = getFieldState(key);
    return {
      ...init,
      [key]: !state.invalid,
    };
  }, {});

  const anyDirty = Object.keys(dirtyFields || {}).length > 0;

  return {
    form: {
      fields: values,
      changed: dirtyFields || {},
      values,
      valid,
      errors,
      touched: touchedFields || {},
      status: {
        dirty: anyDirty,
        valid: anyDirty ? isValid : false,
        touched: !!Object.keys(touchedFields)?.length,
        submitting: isSubmitting,
        lastFieldChanged: '',
      },
    },
    SetValue: setValue,
    SetError: setError,
    RegisterField: register,
    UnregisterField: unregister,
    ResetForm: reset,
    SetTouched: (key: any) => {
      const val = getValues(key);
      setValue(key, val, { shouldTouch: true, shouldDirty: true, shouldValidate: true });
      trigger(key);
    },
  };
};

export const FormConnector = (props: FormConnectorProps) => {
  const { children } = props;
  const formContext = useFormContext<FieldValues>();
  return <>{children({ ...mapConnectorProps(formContext) })}</>;
};
