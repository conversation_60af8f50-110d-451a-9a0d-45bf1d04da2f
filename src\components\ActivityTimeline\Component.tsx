import { ErrorMessage, LoadableSection, ModalWindow, Snackbar } from "cx-dle-component-library";
import { ActivityTimeline } from 'cx-dle-component-library/components/ActivityTimeline';
import query from './query';
import { useQuery } from "@apollo/client";
import { useEffect, useState } from "react";
import './styles.scss';
import { getLocaleFromHostname, getLocalizedModality } from "../../utils";
import { useDocumentDownload } from "../../hooks/download/useDocumentDownload";
import { ActivityTimelineWrapperProps } from "./models";


const ActivityTimelineWrapperControl = (props: ActivityTimelineWrapperProps) => {

  // State to show loading spinner in the modal while data is loading and is available in the state
  const [loading, setLoading] = useState(true);
  // State to show red toaster in case of API failure or required data is not available
  const [error, setError] = useState(false);
  const [showAlert, setShowAlert] = useState(false);

  const {
    fetchDocuments,
    documentSearchResults,
    loading: downloadDocumentsLoading,
    hasError: downloadDocumentsError,
    showPopover,
    setShowPopover,
    handleDownload: handleDownloadPopover,
  } = useDocumentDownload();

  const locale = getLocaleFromHostname();

  const { data, loading: activityTimelineLoading, error: queryError } = useQuery(query, {
    variables: { serviceEventId: props.seviceEventId, isPartsDataEnabled: props.activityTimelineStaticData.enablePartStatus },
    fetchPolicy: "no-cache",
    onCompleted: (data) => {
      if (!data?.serviceEvent || !data?.serviceEventLifecycle) {
        console.error('Error displaying activity timeline as sufficient data is not available.', data);
        setError(true);
      }
      // Set loading state to false once data is received
      setLoading(false);
    },

  }
  );

  const selectedDocuments =
    documentSearchResults?.documents?.filter((document) => props?.selectedItems?.includes(document?.id)) ?? [];

  // Set error state to display red toaster
  useEffect(() => {
    if (queryError) {
      setError(true);
      setLoading(false);
    }

    if (downloadDocumentsError) {
      setShowAlert(true);
    }
  }, [queryError, downloadDocumentsError]);

  useEffect(() => {
    setShowAlert(error);
  }, [error]);

  useEffect(() => {
    if (documentSearchResults && documentSearchResults?.documents?.length > 0) {
      const documentCollection = { results: documentSearchResults };
      props?.setDownloadDocumentCollection(documentCollection);
    }
  }, [documentSearchResults]);

  return (
    <>
      <ModalWindow isScrollable={true} className="ge-activity-timeline" closeWindow={props.handleCloseButton}>
        {activityTimelineLoading ? (
          <LoadableSection
            loadingProps={{ fullHeight: true }}
            loading={loading}
            error={error}
          >
            {() => {
              return null;
            }}
          </LoadableSection>
        ) :
          data?.serviceEvent &&
          (<LoadableSection
            loadingProps={{ fullHeight: true }}
            loading={loading}
            error={error}
          >
            {() => {
              const serviceEventData = JSON.parse(JSON.stringify(data));
              serviceEventData.serviceEvent.asset = serviceEventData.serviceEvent.assetV2;
              serviceEventData.serviceEvent.activities = serviceEventData.serviceEvent.activitiesV2;

              const modality = getLocalizedModality(props.timelineLocalizationData, serviceEventData.serviceEvent.assetV2.modalityId)

              const modalityDisplayName = modality ? modality?.modalityDisplayName : ''

              if (serviceEventData?.serviceEvent?.asset && modality) {
                serviceEventData.serviceEvent.asset.modalityDisplayName =
                  modalityDisplayName;
              }
              return (
                <ActivityTimeline
                  serviceEventId={props.seviceEventId}
                  serviceEventData={serviceEventData}
                  downloadSpinner={downloadDocumentsLoading}
                  setDownloadSpinner={(downloadSpinner) => !downloadSpinner}
                  onDownloadLinkClick={(downloadProps) => {
                    if (downloadProps.toggle) {
                      handleDownloadPopover(selectedDocuments);
                    }
                    else {
                      const regex = /[-]/g;
                      const variables = {
                        documentType: downloadProps?.fileItems?.downloadFileType?.map(({ code, value }) => code ?? value),
                        serviceRequestNumber: downloadProps?.documentHelperRequestProps?.servicerequestId as string,
                        source: downloadProps?.fileItems?.sourceType as string,
                        locale: regex?.test(locale) ? locale : 'en-us',
                      };
                      fetchDocuments({ variables });
                    }
                  }}
                  toggle={showPopover}
                  updateToggle={setShowPopover}
                  {...props}
                  {...props.activityTimelineStaticData}
                  modalityDisplayName={modalityDisplayName}
                />)
            }}
          </LoadableSection>
          )}

        {showAlert && (
          <Snackbar
            type="danger"
            showCloseButton={true}
            onCloseClick={() => {
              setShowAlert(false);
            }}
          >
            <ErrorMessage message={props?.componentInternalError} />
          </Snackbar>
        )}
      </ModalWindow>
    </>
  );
}

export default ActivityTimelineWrapperControl