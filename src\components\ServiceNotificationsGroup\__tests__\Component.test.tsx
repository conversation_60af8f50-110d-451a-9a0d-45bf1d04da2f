import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { ServiceNotificationsGroup } from '../Component';
import { MockedProvider } from '@apollo/client/testing';
import { updateNotificationPreferenceMutation } from '../mutation';
import { ServiceNotificationsGroupControlProps } from '../models';

// Mock required components and dependencies
jest.mock('cx-dle-component-library', () => ({
  GridCell: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  GridRow: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  RichText: ({ text }: { text: string }) => <div>{text}</div>,
  Text: ({ text }: { text: string }) => <div>{text}</div>,
  GeButton: ({ btnLabel, onClick }: { btnLabel: string; onClick: () => void }) => (
    <button onClick={onClick}>{btnLabel}</button>
  ),
  Snackbar: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  ErrorMessage: ({ message }: { message: string }) => <div>{message}</div>,
  Checkbox: ({
    children,
    value,
    name,
    className,
    changeHandler,
    ...props
  }: {
    children: React.ReactNode;
    value: boolean;
    name: string;
    className: string;
    changeHandler: (event: { target: { checked: boolean } }) => void;
  }) => (
    <div>
      <input type="checkbox" checked={value} name={name} onChange={changeHandler} {...props} />
      {children}
    </div>
  ),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

const mockUpdateNotificationPreferences = jest.fn();
jest.mock('../../../context/NotificationsPreferencesContext', () => ({
  useNotificationsPreferences: () => ({
    updateNotificationPreferences: mockUpdateNotificationPreferences,
    preferences: [],
    setSelectedPreferences: jest.fn(),
  }),
}));

describe('ServiceNotificationsGroup', () => {
  const defaultProps: ServiceNotificationsGroupControlProps = {
    settings: [
      {
        groupKey: 'Test Group',
        offLabelText: 'Off',
        onLabelText: 'On',
        selectedValues: [
          {
            appName: 'MyEquipment',
            channelType: 'in-app',
            code: 'ServiceRequestStatusReceived',
            description: 'Receive notifications when service is requested on your equipment',
            disableSelection: false,
            emailPreferenceDescription: 'Receive emails when service is requested on your equipment',
            groupName: 'Unplanned Service',
            hasAccess: true,
            isChecked: true,
            name: 'Request Received',
            richDescription: '',
            title: 'Request Received',
          },
        ],
        disableToggle: false,
      },
    ],
    onChange: jest.fn(),
    saveButtonText: 'Save',
    discardButtonText: 'Discard',
    onSave: jest.fn(),
    onError: jest.fn(),
    notificationChannel: 'email',
    notificationType: [],
    completeList: [],
    categoryDefinition: ['Test Category'],
  };

  const mocks = [
    {
      request: {
        query: updateNotificationPreferenceMutation,
        variables: {
          input: {
            notificationType: [
              {
                appName: 'MyEquipment',
                notificationChannel: 'email',
                notificationCode: 'TEST_CODE',
                preferred: false,
              },
            ],
          },
        },
      },
      result: {
        data: {
          updateUserPreferences: {
            notificationType: [],
          },
        },
      },
    },
  ];

  it('renders component with initial state', () => {
    render(
      <MockedProvider mocks={mocks}>
        <ServiceNotificationsGroup {...defaultProps} />
      </MockedProvider>,
    );

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Test Category')).toBeInTheDocument();
  });

  it('handles toggle switch change', () => {
    render(
      <MockedProvider mocks={mocks}>
        <ServiceNotificationsGroup {...defaultProps} />
      </MockedProvider>,
    );

    const toggleSwitch = screen.getAllByRole('checkbox')[1];
    fireEvent.click(toggleSwitch);
    expect(defaultProps.onChange).toHaveBeenCalled();
  });

  it('handles save button click', async () => {
    render(
      <MockedProvider mocks={mocks}>
        <ServiceNotificationsGroup {...defaultProps} />
      </MockedProvider>,
    );

    // Enable save button by making a change
    const toggleSwitch = screen.getAllByRole('checkbox')[1];
    fireEvent.click(toggleSwitch);

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    expect(mockUpdateNotificationPreferences).toHaveBeenCalled();
  });

  it('handles discard button click', () => {
    render(
      <MockedProvider mocks={mocks}>
        <ServiceNotificationsGroup {...defaultProps} />
      </MockedProvider>,
    );

    const discardButton = screen.getByText('Discard');
    fireEvent.click(discardButton);
    expect(defaultProps.onChange).toHaveBeenCalled();
  });
});
