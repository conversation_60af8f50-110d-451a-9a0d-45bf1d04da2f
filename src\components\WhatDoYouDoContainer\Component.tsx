import { validationSchema } from 'cx-dle-common-lib';
import { FormSection, GridCell, GridRow } from 'cx-dle-component-library';
import { FORM_FIELDS } from '../../constants';
import { WhatDoYouDoContainerProps } from './models';
import './styles.scss';
import GeSelect from '../Common/GeSelect';
import { WhatDoYouDoFacilityType } from '../WhatDoYouDoFacilityType/Component';

export const WhatDoYouDoContainer = ({
  whatDoYouDoCaption,
  isFormValid,
  roleTitle,
  roleCategories,
  websiteCountryCode,
  defaultValues,
  roleProfessionOptional,
  jobTitleRequiredValidationMessage,
  roleProfessionLabel,
  roleProfessionOptionalRequiredLabel,
  departmentLabel,
  roleProfessionPlaceholder,
  roleProfessionSelectItems,
  departmentOptionalRequiredLabel,
  departmentPlaceholder,
  departmentSelectItems,
  departmentOptional,
  departmentRequiredValidationMessage,
  submitState,
  selectedFacilityType,
  setSelectedFacilityType
}: WhatDoYouDoContainerProps) => {

  return (
    <>
    {(!defaultValues[FORM_FIELDS.ROLE]?.value || !defaultValues[FORM_FIELDS.DEPARTMENT]?.value ) && <FormSection
      className={
        websiteCountryCode == 'ZA' || websiteCountryCode == 'FR'
          ? 'what-do-you-do-container what-do-you-do-container-ZA'
          : 'what-do-you-do-container'
      }
      titleText={whatDoYouDoCaption}
    >
      {websiteCountryCode === 'JP' && (
        <WhatDoYouDoFacilityType
          roleCategories={roleCategories}
          roleTitle={roleTitle}
          isFormValid={isFormValid}
          onFacilityTypeSelected={setSelectedFacilityType}
          facility={selectedFacilityType}
        />
      )}
 
        <GridRow>
          {!defaultValues[FORM_FIELDS.ROLE]?.value && <GridCell desktop={6} tablet={6} phone={12}>
            <div className="what-do-you-do-department">
              <GeSelect
                className="text-required"
                name={FORM_FIELDS?.ROLE}
                labelText={roleProfessionLabel}
                requiredText={roleProfessionOptionalRequiredLabel}
                disabled={
                  defaultValues[FORM_FIELDS.ROLE]?.value || (submitState?.status === 'Loading')
                }
                defaultValue={defaultValues[FORM_FIELDS.ROLE]}
                placeholder={roleProfessionPlaceholder}
                options={roleProfessionSelectItems.map(({ title }) => ({
                  label: title,
                  value: title,
                }))}
                required={!roleProfessionOptional}
                validate={
                  !roleProfessionOptional
                    ? validationSchema().required(jobTitleRequiredValidationMessage || 'A job role is required')
                    : undefined
                }
              />
            </div>
          </GridCell>}
          {!defaultValues[FORM_FIELDS.DEPARTMENT]?.value &&<GridCell desktop={6} tablet={6} phone={12}>
            <div className="what-do-you-do-role">
              <GeSelect
                className="text-required"
                name={FORM_FIELDS.DEPARTMENT}
                labelText={departmentLabel}
                requiredText={departmentOptionalRequiredLabel}
                disabled={
                  defaultValues[FORM_FIELDS.DEPARTMENT]?.value || (submitState?.status === 'Loading')
                }
                defaultValue={defaultValues[FORM_FIELDS.DEPARTMENT]}
                placeholder={departmentPlaceholder}
                options={departmentSelectItems.map(({ title }) => ({
                  label: title,
                  value: title,
                }))}
                required={!departmentOptional}
                validate={
                  !departmentOptional
                    ? validationSchema().required(departmentRequiredValidationMessage || 'Department is required')
                    : undefined
                }
              />
            </div>
          </GridCell>}
        </GridRow>
    </FormSection>}
    </>
  );
};
