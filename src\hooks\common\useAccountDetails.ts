import { useMemo } from 'react';
import { UserDetails, UserInfo } from './useUserInfo';
import { MY_EQUIPMENT, MY_ORDERS, SECURITY, TRAINING } from '../../constants';

export const userAccountDetails = (userInfo?: UserInfo) => {
  const profileInfo = useMemo(() => (userInfo?.userAccountDetails as UserDetails) || null, [userInfo]);

  const isHoldingAccount = useMemo(() => userInfo?.userAccountDetails?.isHoldingAccount, [userInfo]);

  const organization = useMemo(() => {
    if (userInfo?.userAccountDetails && !isHoldingAccount) {
      return userInfo?.userAccountDetails?.accountName;
    }
    return null;    
  }, [userInfo]);

  const hasEquipmentAccess = useMemo(
    () =>
      !!userInfo?.userAccountDetails?.assignedApplications?.some(
        ({ applicationName }) => applicationName === MY_EQUIPMENT,
      ),
    [userInfo],
  );

  const hasCyberAccess = useMemo(
    () =>
      !!userInfo?.userAccountDetails?.assignedApplications?.some(
        ({ applicationName }) => applicationName === SECURITY,
      ),
    [userInfo],
  );

  const hasOrdersAccess = useMemo(
    () =>
      !!userInfo?.userAccountDetails?.assignedApplications?.some(
        ({ applicationName }) => applicationName === MY_ORDERS,
      ),
    [userInfo],
  );

  const hasTrainingAccess = useMemo(
    () =>
      !!userInfo?.userAccountDetails?.assignedApplications?.some(({ applicationName }) => applicationName === TRAINING),
    [userInfo],
  );

  return {
    profileInfo,
    organization,
    hasEquipmentAccess,
    hasOrdersAccess,
    hasTrainingAccess,
    isHoldingAccount,
    hasCyberAccess
  };
};
