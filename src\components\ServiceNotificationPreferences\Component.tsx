import { useLazyQuery, useQuery } from '@apollo/client';
import { OMNITURE_EVENTS } from 'cx-dle-common-lib';
import { LoadableSection, RichText, Snackbar } from 'cx-dle-component-library';
import { useEffect, useMemo, useState } from 'react';
import { AssignedApplication, usePageData, useUserInfo } from '../../hooks';
import PreferencesCard from '../Common/PreferencesCard';
import { PreferencesCardContent } from '../Common/PreferencesCard/components';
import SelectedPreferences from '../SelectedPreferences';
import { ServiceNotificationCard } from '../ServiceNotificationCard/Component';
import ServiceNotificationsGroup from '../ServiceNotificationsGroup';
import {
  GroupCheckList,
  NotificationsGroupSettingsExtended,
  NotificationType,
  SelectedGroupItemList,
  ServiceNotificationPreferenceProps,
} from './models';
import { notificationPreferencesQuery } from './query';
import { SfdcNotificationsFlagsLocalizationType, Sitecommoncontent } from '../../models/Sitecommoncontent';
import { ItemType } from '../../models/CommunicationsTab';
import { useTranslation } from 'react-i18next';
import { shouldShowComponent } from '../../utils/commonUtils';
import { useNotificationsPreferences } from '../../context/NotificationsPreferencesContext';
import { assignNotificationTypeList, uniqueValues } from './utils';

export const ServiceNotificationPreferences: React.FC<ServiceNotificationPreferenceProps> = (props) => {
  // Consolidated state
  const [showError, setShowError] = useState(false);
  const [UpdatedPreferences, setUpdatedPreferences] = useState<GroupCheckList[]>([]);
  const [checkedItems, setCheckedItems] = useState<GroupCheckList[]>([]);
  const [itemsWithAccess, setItemsWithAccess] = useState(true);

  // Hooks
  const { data: userInfo, loading: userInfoLoading } = useUserInfo();
  const { data, loading: notificationsLoading } = useQuery(notificationPreferencesQuery, {
    fetchPolicy: 'no-cache',
  });
  const [getNotificationPreferences, { data: lazyData }] = useLazyQuery(notificationPreferencesQuery, {
    fetchPolicy: 'no-cache',
  });
  const { data: commonData, isLoading: commonDataLoading } = usePageData<Sitecommoncontent>(
    'sitecommoncontent',
    'sitecommoncontentData',
  );
  const { t } = useTranslation();

  // local variables
  let configuredNotificationTypes = [] as NotificationType[];
  const categoryDefinitions = props.groups.map((item) => item.categoryDefinition);
  const { sfdcNotificationsFlagsLocalization } = commonData || {};
  const setChannels = (typeData: any) => {
    return typeData !== undefined ? typeData.notificationChannel : '';
  };

  // context
  const { updateNotificationPreferences, selectedPreferences, setSelectedPreferences, isDataSaved, setDataSaved } =
    useNotificationsPreferences();

  const loading = useMemo(
    () => userInfoLoading || notificationsLoading || commonDataLoading,
    [notificationsLoading, userInfoLoading, commonDataLoading],
  );

  const getUpdatedNotificationList = (
    notificationTypeList: NotificationType[],
    updatedNotificationTypeList: NotificationType[],
  ) => {
    const notificationsPreferred = updatedNotificationTypeList;

    const key = (n: NotificationType) => `${n.notificationChannel}|${n.notificationCode}`;

    const supersetMap = new Map<string, NotificationType>();

    [...notificationTypeList, ...notificationsPreferred].forEach((item) => {
      supersetMap.set(key(item), item);
    });

    const tempSettings: NotificationType[] = Array.from(supersetMap.values());

    return tempSettings as NotificationType[];
  };

  const checkAccess = (applications: AssignedApplication[], applicationName: string) => {
    return applications?.some((m) => m.applicationName === applicationName);
  };

  // Effects
  useEffect(() => {
    // once user clicks on save button, this lazy query will call notification query to fetch fresh data without refresh
    const fetchNotificationPreferences = async () => {
      try {
        if (isDataSaved) {
          await getNotificationPreferences();
        }
      } catch (error) {
        console.error('Error fetching notification preferences:', error);
        setShowError(true);
      }
    };

    fetchNotificationPreferences();
  }, [isDataSaved, getNotificationPreferences]);


  useEffect(() => {
    const assignedApplications = userInfo?.userAccountDetails?.assignedApplications || [];
    if (!data || !assignedApplications || !sfdcNotificationsFlagsLocalization) return;

    let checkedItemList = checkedItems;
    props.groups.map((grpItem: NotificationsGroupSettingsExtended) => {
      const { disableToggle, groupTitle, offLabelText, onLabelText, productType } = grpItem;
      const checkboxList = [] as SelectedGroupItemList[];

      grpItem.items.map((setting) => {
        const item = setting as ItemType;

        let typeData;
        if (lazyData) {
          typeData = lazyData.collection.notificationType.filter((val: NotificationType) =>
            val.notificationChannel.toLowerCase().match(props.notificationType.name.toLowerCase()),
          );
          const matched = typeData.find((m: NotificationType) => m.notificationCode === item.code);
          configuredNotificationTypes.push(matched);
          configuredNotificationTypes = configuredNotificationTypes.filter((x) => x !== undefined);
        } else {
          if (data) {
            typeData = data.collection.notificationType
              .filter((val: NotificationType) =>
                val.notificationChannel.toLowerCase().match(props.notificationType.name.toLowerCase()),
              )
              .find((m: NotificationType) => m.notificationCode === item.code);

            configuredNotificationTypes.push(typeData);
            configuredNotificationTypes = configuredNotificationTypes.filter((x) => x !== undefined);
          }
        }

        const channelValue = setChannels(typeData);

        const groupObject = {
          appName: productType.value,
          channelType: channelValue,
          code: item.code,
          description: item.description,
          disableSelection: item.disableSelection,
          emailPreferenceDescription: item.emailPreferenceDescription,
          groupName: groupTitle,
          hasAccess: checkAccess(assignedApplications, productType.value),
          isChecked: typeData !== undefined ? typeData.preferred : false,
          name: item.name,
          richDescription: item.richDescription,
          title: item.title,
        };
        checkboxList.push(groupObject);
      });

      if (
        (checkedItemList.length === 0 || checkedItemList.every((item) => item.groupKey !== groupTitle)) &&
        UpdatedPreferences.length === 0
      ) {
        checkedItemList.push({
          disableToggle: disableToggle,
          groupKey: groupTitle,
          offLabelText,
          onLabelText,
          selectedValues: checkboxList,
        });
      }
    });

    // state is updated from onchange method
    let notificationTypeList: NotificationType[] = [];
    if (UpdatedPreferences.length > 0) {
      checkedItemList = UpdatedPreferences;

      UpdatedPreferences.forEach((nData) => {
        nData.selectedValues.forEach((nSelected) => {
          const retValues = assignNotificationTypeList(nSelected, props.notificationType.name as string);
          notificationTypeList = notificationTypeList.concat(retValues);
        });
      });

      notificationTypeList = uniqueValues(notificationTypeList);
    }
    setCheckedItems(checkedItemList);

    const notificationList = getUpdatedNotificationList(configuredNotificationTypes, notificationTypeList);

    updateNotificationPreferences(notificationList);

    //
    if (isDataSaved && lazyData) {
      setSelectedPreferences(notificationList, props.notificationType.name, sfdcNotificationsFlagsLocalization);
    } else {
      setSelectedPreferences(
        configuredNotificationTypes,
        props.notificationType.name,
        sfdcNotificationsFlagsLocalization,
      );
    }
    setItemsWithAccess(!!checkedItemList.filter((item) => item.selectedValues.every((x) => x.hasAccess)).length);
    //setDataLoaded(true);
  }, [data, UpdatedPreferences, lazyData, isDataSaved, sfdcNotificationsFlagsLocalization]);

  const linkTitle = `${props?.notificationType?.name}-preferences`;

  return (
    <>
      {showError && (
        <Snackbar
          showCloseButton={true}
          type="danger"
          onCloseClick={() => {
            setShowError(false);
          }}
        >
          <RichText text={t('Errors.Default')} tag="span" />
        </Snackbar>
      )}
      {loading && (
        <LoadableSection loading={loading} error={false}>
          {() => null}
        </LoadableSection>
      )}
      {!sfdcNotificationsFlagsLocalization && (
        <LoadableSection loading={loading} error={false}>
          {() => null}
        </LoadableSection>
      )}
      {!loading &&
        // dataLoaded &&
        data &&
        itemsWithAccess &&
        shouldShowComponent(props?.supportedCountries, userInfo?.userAccountDetails.country || '') && (
          <PreferencesCard title={props.notificationType.title}>
            <PreferencesCardContent
              closeLinkText={props.closeLinkText}
              editLinkText={props.editLinkText}
              analyticsAttributes={[
                { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                { name: 'linkType', value: 'PreferencesCard' },
                { name: 'linkTitle', value: linkTitle },
              ]}
              shortContent={
                <ServiceNotificationCard
                  closeLinkText={props.closeLinkText}
                  editLinkText={props.editLinkText}
                  infoTitle={props.infoTitle}
                  descriptionLabel={props.infoDescription}
                  showDescriptionLabel={!selectedPreferences.length}
                  preferencesComponent={
                    <SelectedPreferences preferences={selectedPreferences} preferencesThreshold={3} />
                  }
                />
              }
              expandedContent={
                <ServiceNotificationsGroup
                  settings={checkedItems}
                  onError={() => setShowError(true)}
                  discardButtonText={props.discardButtonText}
                  onChange={(updatedSettings: GroupCheckList[]) => {
                    setUpdatedPreferences([...updatedSettings]);
                    setDataSaved(false);
                  }}
                  saveButtonText={props.saveButtonText}
                  notificationType={configuredNotificationTypes}
                  notificationChannel={props.notificationType.name}
                  completeList={data.collection.notificationType}
                  categoryDefinition={categoryDefinitions}
                  sfdcNotificationsFlagsLocalization={
                    sfdcNotificationsFlagsLocalization as SfdcNotificationsFlagsLocalizationType
                  }
                  onSave={() => {
                    // using this flag to check if save is successful
                    setDataSaved(true);
                  }}
                />
              }
            />
          </PreferencesCard>
        )}
    </>
  );
};
