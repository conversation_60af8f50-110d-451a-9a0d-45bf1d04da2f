import { useLazyQuery } from '@apollo/client';
import { OMNITURE_EVENTS, prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import { AlertMessage, ErrorMessage, Snackbar } from 'cx-dle-component-library';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AVURI_DEVICE_MANAGEMENT,
  LAUNCH_DARKLY_FLAGS,
  MY_EQUIPMENT,
  MY_ORDERS,
  PROGRESSIVE_REGISTRATION_STATUS,
  SERVICE_SHOP_ID,
  VIEW_POOL_CONTRACTS_ID,
  DIRECT_USER,
  CP_USER,
  CP_ADMIN_USER,
  CHECK_CYBER_SECURITY_UPDATES,
  VIEW_MANUALS,
} from '../../constants';
import { isCSMReadOnly } from '../../utils';
import EquipmentServiceCard from '../EquipmentServiceCard';
import { CapabilitiesProps } from './models';
import { experienceFilteringPreferencesWidgetQuery } from './query';
import './styles.scss';
import { EquipmentInventoryServiceType } from '../../models/Account';
import OrdersAndBilling from '../OrdersAndBilling';
import getUserDetailsFromCookies, { isCSMMode } from '../../utils/helpers';
import { CapabilitiesAlertContainer, TitleLoaderSection } from './SkeletonLoader/Component';
import { userAccountDetails } from '../../hooks/common/useAccountDetails';

export const Capabilities = (props: CapabilitiesProps) => {
  const enabledServices: EquipmentInventoryServiceType[] = [];
  const disabledServices: EquipmentInventoryServiceType[] = [];

  props.ordersBilling?.map((currentElement) => {
    const matchedService = props.enableServicesIfOrdersNotApproved?.find(
      (service) => service.name == currentElement.name,
    );
    if (matchedService) {
      enabledServices.push(matchedService as EquipmentInventoryServiceType);
    } else {
      disabledServices.push(currentElement);
    }
  });
  const [setupAmount, setSetupAmount] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [status, setStatus] = useState('');
  const [ordersStatus, setOrdersStatus] = useState('');
  const [hasVerisoundFleetAccess, setHasVerisoundFleetAccess] = useState(false);
  const [hasOrdersAccess, setHasOrdersAccess] = useState(false);
  const [CSMError, setCSMError] = useState(false);
  const [isSetupPopUpVisible, setIsSetupPopUpVisible] = useState(true);
  const isAccount2 = !!props.enableAccount2Experience;
  const { userInfo, modalityData, userProgressiveRegistrations, loading } = props;
  const { hasEquipmentAccess, hasCyberAccess, isHoldingAccount } = userAccountDetails(userInfo);

  const { t } = useTranslation();

  let filteredOtherServices = props?.otherServices;
  let userCateogry = DIRECT_USER;

  if (
    userInfo?.userAccountDetails?.userType?.toLowerCase() === props.userType.CP.toLowerCase() &&
    !userInfo?.userAccountDetails.custAdmin
  ) {
    userCateogry = CP_USER;
    filteredOtherServices = props.otherServices.filter(
      (item) => item.id === CHECK_CYBER_SECURITY_UPDATES || item.id === VIEW_MANUALS,
    );
  }

  if (
    userInfo?.userAccountDetails?.userType?.toLowerCase() === props.userType.CP.toLowerCase() &&
    userInfo?.userAccountDetails.custAdmin
  ) {
    userCateogry = CP_ADMIN_USER;
    filteredOtherServices = props.otherServices.filter(
      (item) => item.id === CHECK_CYBER_SECURITY_UPDATES || item.id === VIEW_MANUALS,
    );
  }
  // Hide equipment experience for few user regions
  const user = getUserDetailsFromCookies();

  let enableServiceShopFor = true;
  let filterdEquipmentServices = JSON.parse(JSON.stringify(props?.equipmentServices));
  let userMailingCountry;

  if (isCSMMode()) {
    userMailingCountry = userInfo?.userAccountDetails.country;
  } else {
    userMailingCountry = user?.address?.country;
  }

  const isEquipmentServiceEnabled = props.enableEquipmentServicesFor?.some(
    (enabledCountry) => enabledCountry === userMailingCountry,
  );

  const isAnalyticsInsightsEnabled = props?.enableAnalyticsInsightsFor?.some(
    (enabledCountry) => enabledCountry === userMailingCountry,
  );

  const isOrdersBillingEnabled = props?.enableOrdersBillingFor?.some(
    (enabledCountry) => enabledCountry === userMailingCountry,
  );

  if (props.enableServiceShopFor?.length > 1) {
    enableServiceShopFor = props.enableServiceShopFor?.some((enabledCountry) => enabledCountry === userMailingCountry);
    //Country not match with user address country then removed service shop card
    if (!enableServiceShopFor) {
      filterdEquipmentServices = props?.equipmentServices.filter((item) => {
        return item?.id !== SERVICE_SHOP_ID;
      });
    }
  }

  const modalityIds = useMemo(() => {
    const modalityLocalization = modalityData?.modalityLocalizations;
    if (!modalityLocalization) return null;

    return modalityLocalization.filter((element) => element.isActive).map((item) => item.modalityId);
  }, [modalityData?.modalityLocalizations]);

  const popupData = {
    toolTipButtonLabel: props?.toolTipButtonLabel,
    toolTipDescriptionLabel: props?.toolTipDescriptionLabel,
    toolTipTitleLabel: props?.toolTipTitleLabel,
  };

  useEffect(() => {
    if (userProgressiveRegistrations?.userProgressiveRegistrations) {
      const myEquipmentResponse = userProgressiveRegistrations?.userProgressiveRegistrations?.find(
        ({ application }) => application === MY_EQUIPMENT,
      );
      const statusFromResponse = myEquipmentResponse?.cdxStatus || PROGRESSIVE_REGISTRATION_STATUS.CLOSED;
      setStatus(statusFromResponse);

      const myOrdersResponse = userProgressiveRegistrations?.userProgressiveRegistrations?.find(
        ({ application }) => application === MY_ORDERS,
      );

      const ordersStatusFromResponse = myOrdersResponse?.cdxStatus || PROGRESSIVE_REGISTRATION_STATUS.CLOSED;

      const isOrdersAssigned = userInfo?.userAccountDetails?.assignedApplications?.some(
        ({ applicationName }) => applicationName === MY_ORDERS,
      );

      setHasOrdersAccess(
        isOrdersAssigned ||
          myOrdersResponse?.cdxStatus === PROGRESSIVE_REGISTRATION_STATUS.CLOSED ||
          myOrdersResponse?.cdxStatus === PROGRESSIVE_REGISTRATION_STATUS.IN_PROGRESS ||
          myOrdersResponse?.cdxStatus === PROGRESSIVE_REGISTRATION_STATUS.PENDING ||
          myOrdersResponse?.cdxStatus === PROGRESSIVE_REGISTRATION_STATUS.FAILURE ||
          myOrdersResponse?.cdxStatus === PROGRESSIVE_REGISTRATION_STATUS.REJECTED,
      );

      setHasVerisoundFleetAccess(
        userProgressiveRegistrations?.userProgressiveRegistrations?.find(
          ({ application, cdxStatus }) =>
            application === AVURI_DEVICE_MANAGEMENT && cdxStatus === PROGRESSIVE_REGISTRATION_STATUS.CLOSED,
        ),
      );

      setOrdersStatus(ordersStatusFromResponse);
    }
  }, [userProgressiveRegistrations]);

  //// Using lazy query
  const [triggerQuery, { data: experienceFilterData }] = useLazyQuery(experienceFilteringPreferencesWidgetQuery);

  // Wrap the lazy query in a custom function
  const experienceFilteringQuery = (modalityIds: any) => {
    triggerQuery({
      variables: { localizedModalityIds: modalityIds },
    });
  };

  useEffect(() => {
    if (!isAccount2 && modalityIds) {
      experienceFilteringQuery(modalityIds);
    }
  }, [isAccount2, modalityIds]);

  useEffect(() => {
    if (userInfo) {
      if (
        userInfo?.userAccountDetails?.onboarded?.mobile ||
        userInfo?.userAccountDetails?.onboarded?.desktop ||
        isAccount2
      ) {
        setIsSetupPopUpVisible(false);
      }
    }
  }, [userInfo]);

  useEffect(() => {
    if (experienceFilterData) {
      setSetupAmount(experienceFilterData?.counts?.assetsFollowingCount?.followingCount);
      setTotalAmount(experienceFilterData?.counts?.assetsFollowingCount?.totalCount);
    }
  }, [experienceFilterData]);

  useEffect(() => {
    const handleClick = (e: Event) => checkCSMMode(e);
    setTimeout(() => {
      document.getElementsByClassName('EquipmentStatusAlertLink')[0]?.addEventListener('click', handleClick);
      document.getElementsByClassName('capabilities-status-alert__link')[0]?.addEventListener('click', handleClick);
      document.querySelectorAll('a.Link')?.forEach((anchor) => {
        anchor.addEventListener('click', handleClick);
      });
    });
  }, []);

  const isApplicationPending = (status: string) => {
    return status === PROGRESSIVE_REGISTRATION_STATUS.IN_PROGRESS || status === PROGRESSIVE_REGISTRATION_STATUS.PENDING;
  };

  const isApplicationRejected = (status: string) => {
    return status === PROGRESSIVE_REGISTRATION_STATUS.FAILURE || status === PROGRESSIVE_REGISTRATION_STATUS.REJECTED;
  };

  const checkCSMMode = (e: any) => {
    if (isCSMReadOnly()) {
      setCSMError(true);
      e.preventDefault();
    }
  };

  const snackBar = (message: string, close: (visible: boolean) => void) => (
    <Snackbar
      type="danger"
      showCloseButton={true}
      onCloseClick={() => {
        close(false);
      }}
      closeButtonIcon="ico-remove-16"
    >
      <ErrorMessage message={message} />
    </Snackbar>
  );

  const flags = useFlags();
  const viewPoolContractsTileFlag = flags[LAUNCH_DARKLY_FLAGS.ENABLE_PARTS_POOL_USAGE_TILE];

  const filteredAnalyticsInsights = props.analyticsInsights.filter((item) => {
    return item?.id !== VIEW_POOL_CONTRACTS_ID;
  });

  return (
    <>
      <div>{CSMError && snackBar(t('CSM.UserFriendlyMessage'), setCSMError)}</div>
      {/* compare the sitename with account 2 country codes 
      for US: gehc-us sitename compare with account2CountryCodes
      */}
      {isAccount2 && (
        <div className="capabilities-wrapper" key="EquipmentServiceCard">
          {loading && (
            <>
              {<TitleLoaderSection/>}
              {<TitleLoaderSection/>}
              {<TitleLoaderSection/>}
              {<TitleLoaderSection/>}
              {<TitleLoaderSection/>}
            </>
          )}

          {!loading && (
            <>
              { userCateogry === CP_ADMIN_USER && props.accountAdministration.length &&(
                  <>
                    {!!props.accountAdministrationLabel && <h6 className="capabilities-title">{props.accountAdministrationLabel}</h6>}
                    <EquipmentServiceCard
                      cardData={props.accountAdministration}
                      hasAccess={true}
                      equipmentServiceLabel={props.accountAdministrationLabel}
                      defaultCountry={props.defaultCountry}
                      checkCSMMode={checkCSMMode}
                      userMailingCountry={userMailingCountry}
                    />
                  </>
              )}
              {(isHoldingAccount ||
                (!isHoldingAccount && !hasEquipmentAccess) ||
                (hasEquipmentAccess && (isApplicationPending(status) || isApplicationRejected(status)))) && (
                <>
                  {props.training?.length > 0 && userCateogry === DIRECT_USER && (
                    <>
                      {!!props.trainingLabel && <h6 className="capabilities-title">{props.trainingLabel}</h6>}
                      <EquipmentServiceCard
                        cardData={props.training}
                        title={props.secondaryLabel}
                        hasAccess={true}
                        equipmentServiceLabel={props.trainingLabel}
                        defaultCountry={props.defaultCountry}
                        checkCSMMode={checkCSMMode}
                        userMailingCountry={userMailingCountry}
                      />
                    </>
                  )}
                  {(props.equipmentServices?.length > 0 || props.analyticsInsights?.length > 0) && isEquipmentServiceEnabled &&(
                    <div className="capabilities-card-disabled">
                      <>
                        {isHoldingAccount && (
                          <AlertMessage
                            type={'primary'}
                            className="capabilities-status-alert primary"
                            alertIcon={true}
                            alertIconStatus={false}
                          >
                            <CapabilitiesAlertContainer
                              icon={props.accountSetupInProgressIcon}
                              title={props.accountSetupInProgressTitle}
                              description={props.accountSetupInProgressDescription}
                            />
                          </AlertMessage>
                        )}

                        {!isHoldingAccount && isApplicationPending(status) && isEquipmentServiceEnabled && (
                          <AlertMessage
                            type={'primary'}
                            className="capabilities-status-alert primary"
                            alertIcon={true}
                            alertIconStatus={false}
                          >
                            <CapabilitiesAlertContainer
                              icon={props.equipmentInProgressIcon}
                              title={props.equipmentInProgressTitle}
                              description={props.equipmentInProgressDescription}
                            />
                          </AlertMessage>
                        )}

                        {!isHoldingAccount && isApplicationRejected(status) && isEquipmentServiceEnabled && (
                          <AlertMessage
                            type={'danger'}
                            className="capabilities-status-alert danger"
                            alertIcon={true}
                            alertIconStatus={false}
                          >
                            <CapabilitiesAlertContainer
                              icon={props.equipmentRejectedIcon}
                              title={props.equipmentRejectedTitle}
                              description={props.equipmentRejectedDescription}
                            />
                          </AlertMessage>
                        )}

                        {!isHoldingAccount && status === PROGRESSIVE_REGISTRATION_STATUS.CLOSED && isEquipmentServiceEnabled && (
                          <AlertMessage
                            type={'primary'}
                            className="capabilities-status-alert primary"
                            alertIcon={true}
                            alertIconStatus={false}
                          >
                            <CapabilitiesAlertContainer
                              icon={props.equipmentNeedAccessIcon}
                              title={props.equipmentNeedAccessTitle}
                              description={props.equipmentNeedAccessDescription}
                            />
                            <a
                              className="capabilities-status-link"
                              href={props.equipmentNeedAccessSetupLink.href}
                              {...prepareDataAnalyticsAttributes([
                                { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                                { name: 'linkName', value: props.equipmentNeedAccessSetupLink.text },
                                { name: 'linkType', value: 'Equipment Service Card Component' },
                                { name: 'linkTitle', value: props.equipmentServiceLabel },
                              ])}
                              onClick={(event) => {
                                if (isCSMReadOnly()) {
                                  checkCSMMode(event);
                                }
                              }}
                            >
                              {props.equipmentNeedAccessSetupLink.text}
                            </a>
                          </AlertMessage>
                        )}
                      </>

                      {props?.equipmentServices?.length > 0 && isEquipmentServiceEnabled && (
                        <>
                          {!!props?.equipmentServiceLabel && (
                            <h6 className="capabilities-title">{props?.equipmentServiceLabel}</h6>
                          )}
                          <EquipmentServiceCard
                            cardData={filterdEquipmentServices}
                            title={props?.secondaryLabel}
                            hasAccess={true}
                            pendingLabel={props?.pendingEquipmentAccessLabel}
                            rejectedLabel={props?.rejectedEquipmentAccessLabel}
                            popupData={popupData}
                            isSetupPopUpVisible={isSetupPopUpVisible}
                            status={status}
                            loading={loading}
                            setupBtn={props?.setupButton}
                            setupAmount={setupAmount}
                            totalAmount={totalAmount}
                            equipmentServiceLabel={props?.equipmentServiceLabel}
                            defaultCountry={props.defaultCountry}
                            checkCSMMode={checkCSMMode}
                            userMailingCountry={userMailingCountry}
                          />
                        </>
                      )}

                      {props?.analyticsInsights?.length > 0 && isAnalyticsInsightsEnabled && userCateogry === DIRECT_USER && (
                        <>
                          {!!props.analyticsInsigntsLabel && (
                            <h6 className="capabilities-title">{props.analyticsInsigntsLabel}</h6>
                          )}
                          <EquipmentServiceCard
                            cardData={!viewPoolContractsTileFlag ? filteredAnalyticsInsights : props.analyticsInsights}
                            title={props.secondaryLabel}
                            hasAccess={true}
                            equipmentServiceLabel={props.analyticsInsigntsLabel}
                            defaultCountry={props.defaultCountry}
                            checkCSMMode={checkCSMMode}
                            userMailingCountry={userMailingCountry}
                          />
                        </>
                      )}
                    </div>
                  )}
                </>
              )}
              {hasEquipmentAccess && !isApplicationPending(status) && !isApplicationRejected(status) && (
                <>
                  {props?.equipmentServices?.length > 0 && isEquipmentServiceEnabled && (
                    <>
                      {!!props?.equipmentServiceLabel && (
                        <h6 className="capabilities-title">{props?.equipmentServiceLabel}</h6>
                      )}
                      <EquipmentServiceCard
                        cardData={filterdEquipmentServices}
                        title={props?.secondaryLabel}
                        hasAccess={true}
                        equipmentServiceLabel={props?.equipmentServiceLabel}
                        pendingLabel={props?.pendingEquipmentAccessLabel}
                        rejectedLabel={props?.rejectedEquipmentAccessLabel}
                        popupData={popupData}
                        isSetupPopUpVisible={isSetupPopUpVisible}
                        status={status}
                        loading={loading}
                        setupBtn={props?.setupButton}
                        setupAmount={setupAmount}
                        totalAmount={totalAmount}
                        defaultCountry={props.defaultCountry}
                        checkCSMMode={checkCSMMode}
                        userMailingCountry={userMailingCountry}
                      />
                    </>
                  )}

                  {props.analyticsInsights?.length > 0 && isAnalyticsInsightsEnabled && userCateogry === DIRECT_USER && (
                    <>
                      {!!props.analyticsInsigntsLabel && (
                        <h6 className="capabilities-title">{props.analyticsInsigntsLabel}</h6>
                      )}
                      <EquipmentServiceCard
                        cardData={!viewPoolContractsTileFlag ? filteredAnalyticsInsights : props.analyticsInsights}
                        title={props.secondaryLabel}
                        hasAccess={true}
                        equipmentServiceLabel={props.analyticsInsigntsLabel}
                        defaultCountry={props.defaultCountry}
                        checkCSMMode={checkCSMMode}
                        userMailingCountry={userMailingCountry}
                      />
                    </>
                  )}

                  {props?.training?.length > 0 && userCateogry === DIRECT_USER && (
                    <>
                      {!!props?.trainingLabel && <h6 className="capabilities-title">{props?.trainingLabel}</h6>}
                      <EquipmentServiceCard
                        cardData={props?.training}
                        title={props?.secondaryLabel}
                        hasAccess={true}
                        equipmentServiceLabel={props?.trainingLabel}
                        defaultCountry={props.defaultCountry}
                        checkCSMMode={checkCSMMode}
                        userMailingCountry={userMailingCountry}
                      />
                    </>
                  )}
                </>
              )}
              {/* Hide order and billing */}
              {props?.ordersBilling?.length > 0 && isOrdersBillingEnabled && userCateogry === DIRECT_USER && (
                <OrdersAndBilling
                  isHoldingAccount={isHoldingAccount}
                  hasOrdersAccess={hasOrdersAccess}
                  isApplicationPending={isApplicationPending(ordersStatus)}
                  isApplicationRejected={isApplicationRejected(ordersStatus)}
                  ordersBillingLabel={props.ordersBillingLabel}
                  enabledServices={enabledServices}
                  secondaryLabel={props.secondaryLabel}
                  accountSetupInProgressIcon={props.accountSetupInProgressIcon}
                  accountSetupInProgressTitle={props.accountSetupInProgressTitle}
                  accountSetupInProgressDescription={props.accountSetupInProgressDescription}
                  ordersInProgressIcon={props.ordersInProgressIcon}
                  ordersInProgressTitle={props.ordersInProgressTitle}
                  ordersInProgressDescription={props.ordersInProgressDescription}
                  ordersRejectedIcon={props.ordersRejectedIcon}
                  ordersRejectedTitle={props.ordersRejectedTitle}
                  ordersRejectedDescription={props.ordersRejectedDescription}
                  ordersStatus={ordersStatus}
                  ordersNeedAccessIcon={props.ordersNeedAccessIcon}
                  ordersNeedAccessTitle={props.ordersNeedAccessTitle}
                  ordersNeedAccessDescription={props.ordersNeedAccessDescription}
                  ordersNeedAccessSetupLink={props.ordersNeedAccessSetupLink}
                  disabledServices={disabledServices}
                  ordersBilling={props.ordersBilling}
                  defaultCountry={props.defaultCountry}
                  checkCSMMode={checkCSMMode}
                  userMailingCountry={userMailingCountry}
                />
              )}
              {!!props?.otherServiceLabel && (
                <>
                  <h6 className="capabilities-title">{props?.otherServiceLabel}</h6>
                  <>
                    {props?.otherServices?.length > 0 && (
                      <EquipmentServiceCard
                        cardData={filteredOtherServices}
                        hasAccess={true}
                        otherServiceLabel={props?.otherServiceLabel}
                        hasVerisoundFleetAccess={hasVerisoundFleetAccess}
                        defaultCountry={props.defaultCountry}
                        checkCSMMode={checkCSMMode}
                        userMailingCountry={userMailingCountry}
                        isHoldingAccount={isHoldingAccount}
                      />
                    )}
                  </>
                </>
              )}
            </>
          )}
        </div>
      )}
      {!isAccount2 && (
        <div className="capabilities-wrapper" key="EquipmentServiceCard">
          {hasEquipmentAccess && !!props?.equipmentServiceLabel && isEquipmentServiceEnabled && (
            <h6 className="capabilities-title">{props?.equipmentServiceLabel}</h6>
          )}
          {!hasEquipmentAccess && !!props?.primaryLabel && isEquipmentServiceEnabled && (
            <h6 className="capabilities-primary-title">{props?.primaryLabel}</h6>
          )}
          {props?.equipmentServices.length > 0 && isEquipmentServiceEnabled && (
            <EquipmentServiceCard
              cardData={filterdEquipmentServices}
              title={props?.secondaryLabel}
              hasAccess={hasEquipmentAccess}
              pendingLabel={props?.pendingEquipmentAccessLabel}
              rejectedLabel={props?.rejectedEquipmentAccessLabel}
              popupData={popupData}
              isSetupPopUpVisible={isSetupPopUpVisible}
              status={status}
              loading={loading}
              setupBtn={props?.setupButton}
              setupAmount={setupAmount}
              totalAmount={totalAmount}
              equipmentServiceLabel={props?.equipmentServiceLabel}
              defaultCountry={props.defaultCountry}
              checkCSMMode={checkCSMMode}
              userMailingCountry={userMailingCountry}
            />
          )}
          {/* Hide order and billing */}
          {props?.ordersBilling?.length > 0 && isOrdersBillingEnabled && userCateogry === DIRECT_USER && (
            <OrdersAndBilling
              isHoldingAccount={isHoldingAccount}
              hasOrdersAccess={hasOrdersAccess}
              isApplicationPending={isApplicationPending(ordersStatus)}
              isApplicationRejected={isApplicationRejected(ordersStatus)}
              ordersBillingLabel={props.ordersBillingLabel}
              enabledServices={enabledServices}
              secondaryLabel={props.secondaryLabel}
              accountSetupInProgressIcon={props.accountSetupInProgressIcon}
              accountSetupInProgressTitle={props.accountSetupInProgressTitle}
              accountSetupInProgressDescription={props.accountSetupInProgressDescription}
              ordersInProgressIcon={props.ordersInProgressIcon}
              ordersInProgressTitle={props.ordersInProgressTitle}
              ordersInProgressDescription={props.ordersInProgressDescription}
              ordersRejectedIcon={props.ordersRejectedIcon}
              ordersRejectedTitle={props.ordersRejectedTitle}
              ordersRejectedDescription={props.ordersRejectedDescription}
              ordersStatus={ordersStatus}
              ordersNeedAccessIcon={props.ordersNeedAccessIcon}
              ordersNeedAccessTitle={props.ordersNeedAccessTitle}
              ordersNeedAccessDescription={props.ordersNeedAccessDescription}
              ordersNeedAccessSetupLink={props.ordersNeedAccessSetupLink}
              disabledServices={disabledServices}
              ordersBilling={props.ordersBilling}
              defaultCountry={props.defaultCountry}
              checkCSMMode={checkCSMMode}
              userMailingCountry={userMailingCountry}
            />
          )}

          {hasCyberAccess && (
            <>
              {!!props.otherServiceLabel && <h6 className="capabilities-title">{props.otherServiceLabel}</h6>}
              {props.otherServices.length > 0 && (
                <EquipmentServiceCard
                  cardData={filteredOtherServices}
                  hasAccess={hasCyberAccess}
                  otherServiceLabel={props.otherServiceLabel}
                  defaultCountry={props.defaultCountry}
                  checkCSMMode={checkCSMMode}
                  userMailingCountry={userMailingCountry}
                  isHoldingAccount={isHoldingAccount}
                  hasVerisoundFleetAccess={hasVerisoundFleetAccess}
                />
              )}
            </>
          )}
        </div>
      )}
    </>
  );
};
