import { FormConnector, Form } from 'cx-dle-common-lib';
import { GeButton, GridCell, GridContainer, GridRow, LoadableSection, RichText, Snackbar } from 'cx-dle-component-library';
import { Logo } from 'cx-dle-component-library/components/Logo';
import IntroduceYourself from '../../components/IntroduceYourself';
import { usePageData } from '../../hooks/common/usePageData';
import './styles.scss';
import TermsAndConsent from '../../components/TermsAndConsent';
import { WhatDoYouDoContainer } from '../../components/WhatDoYouDoContainer/Component';
import { useEffect, useState } from 'react';
import { currentDomain, VALUE_STRINGS } from '../../constants';
import { UpdatesetupAccountMutation } from './mutations';
import { convertToFullBaseUrl, getLocaleFromHostname } from '../../utils';
import { Metadata } from '../../components/Metadata';
import { useMutation, useQuery } from '@apollo/client';
import getUserDetailsFromCookies, { isCSMMode } from '../../utils/helpers';
import { userProfessionalInfoQuery, useUserInfo } from '../../hooks';

const SetupAccount = () => {
  const { data, isLoading } = usePageData<any>('setup', 'setupdata');
  const [setupErrorBar, setSetupErrorBar] = useState(false);
  const [selectedFacilityTypeValue, setSelectedFacilityTypeValue] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [isDisable, setIsDisable] = useState(false);
  const [updateSetupMutation] = useMutation(UpdatesetupAccountMutation);
  const { data: userInfo, loading: isLoadingUserInfo } = useUserInfo();
  const [hideTnc, setHideTnc] = useState(false);
  const [tncData, setTncData] = useState<any>(false);
  const [IsdCode, setIsdCode] = useState('');
  const { data: userProfessionalData, loading: isLoadingProfessionalData } = useQuery(
  userProfessionalInfoQuery,
  { skip: getLocaleFromHostname() == 'ja-jp'? false : true }
);

  const defaultValues =
  {
    FIRST_NAME: userInfo?.userAccountDetails?.firstName || '',
    LAST_NAME: userInfo?.userAccountDetails?.lastName || '',
    PHONE: '',
    LOCAL_FIRST_NAME: userProfessionalData?.userProfessionalInfo?.contactDetail?.localFirstName ?? '',
    LOCAL_LAST_NAME: userProfessionalData?.userProfessionalInfo?.contactDetail?.localLastName ?? '',
    DEPARTMENT: {
      value: userInfo?.userAccountDetails?.department || '',
      label: userInfo?.userAccountDetails?.department || '',
    },
    ROLE: {
      value: userInfo?.userAccountDetails?.role || '',
      label: userInfo?.userAccountDetails?.role || '',
    },
    TERMS_AND_CONDITIONS: userInfo?.userAccountDetails?.termsAndConditions || false,
  }
  const users = getUserDetailsFromCookies();
  let userMailingCountry;

  if (isCSMMode()) {
    userMailingCountry = userInfo?.userAccountDetails.country;
  } else {
    userMailingCountry = users?.address?.country;
  }

  const basicDeatils = (FunctionalRole: any, Department: any) => {
    let selector = '';

    if (!FunctionalRole) {
      selector = 'hideRole';
    }

    if (!Department) {
      selector = 'hideDept';
    }

    if (FunctionalRole && Department) {
      selector = 'hideDeptRole';
    }

    if (
      userInfo?.userAccountDetails?.country === 'JP' &&
      userInfo?.userAccountDetails?.facilityType === VALUE_STRINGS.DEALER
    ) {
      selector = 'hideDeptRole';
    }
    console.log('selector', selector);

    if (userInfo?.userAccountDetails.termsAndConditions){
      setHideTnc(true);
    }

  };

  useEffect(() => {
    basicDeatils(userInfo?.userAccountDetails?.role, userInfo?.userAccountDetails?.department);
    setIsdCode(selectIsdCodeFromCountrycode(userInfo?.userAccountDetails?.country || 'US', data?.accountSetup?.countryCodeDropDown || []));
  }, [userInfo]);

  const phoneNumberValidation = (value: string, countryCodeDropDown: any[]) => {

    // Find the country object that matches userMailingCountry
    const countryInfo = countryCodeDropDown.find(
      (country) => country.countryISDCode === IsdCode
    );

    // Remove the country ISD code from the phone number first
    let phoneWithoutCountryCode = value;
    if (countryInfo.countryISDCode && value.startsWith(countryInfo.countryISDCode)) {
      phoneWithoutCountryCode = value.substring(countryInfo.countryISDCode.length).trim();
    }

    // Remove any non-digit characters from the remaining phone number
    const phoneDigits = phoneWithoutCountryCode.replace(/\D/g, '');

    const minLength = parseInt(countryInfo.minLength, 10);
    const maxLength = parseInt(countryInfo.maxLength, 10);

    // Check if phone number length is within the valid range
    return phoneDigits.length >= minLength && phoneDigits.length <= maxLength;
  };

  const sourceApplication = '';
  const baseClassName = 'ge-registration-form setup-account';
  const handleSubmit = (values: any) => {
    setIsDisable(true);
    setSubmitting(true);
    updateSetupMutation({
      variables: {
        mutationArguments: {
          department: (userInfo?.userAccountDetails?.country === 'JP' && userInfo.userAccountDetails.facilityType === VALUE_STRINGS.DEALER) ? "" : userInfo?.userAccountDetails.department || values?.DEPARTMENT.value,
          emailOptOut: 'true',
          firstName: values.FIRST_NAME,
          lastName: values.LAST_NAME,
          phone: values.PHONE,
          roleProfession: (userInfo?.userAccountDetails?.country === 'JP' && userInfo.userAccountDetails.facilityType === VALUE_STRINGS.DEALER) ? "" : userInfo?.userAccountDetails.role || values?.ROLE.value,
          localFirstName: values.LOCAL_FIRST_NAME || "",
          localLastName: values.LOCAL_LAST_NAME || "",
          tnCCheck: 'true',
        },
      },
    })
      .then((response) => {
        if (response.data.updateUserInfo.isSuccess) {
          setSubmitting(false);
          window.location.href = data?.accountSetup.confirmationPageLink?.value?.href;
        }
        setSubmitting(false);
      })
      .catch((error) => {
        console.log('Mutation Error:', error);
        setSubmitting(false);
      });
  };
  
  const selectIsdCodeFromCountrycode = (countryCode: string, countryCodeDropDown: any[]) => {
    const countryInfo = countryCodeDropDown.find(
      (country) => country.countryCode === countryCode
    );
    return countryInfo ? countryInfo.countryISDCode : '+1';
  };

  const setAddError = () => {
    setSetupErrorBar(false);
  };


  const checkConditionHandler = (values: any) => {
    if (
      userInfo?.userAccountDetails?.country === 'JP' &&
      userInfo?.userAccountDetails?.facilityType === VALUE_STRINGS.DEALER
    ) {
      return !(
        !submitting &&
        (userInfo.userAccountDetails.termsAndConditions ? userInfo.userAccountDetails.termsAndConditions : tncData) &&
        !!values.FIRST_NAME &&
        !!values.LAST_NAME &&
        !!values.PHONE && (phoneNumberValidation(values.PHONE, data?.accountSetup?.countryCodeDropDown || [])) &&
        !!values.LOCAL_FIRST_NAME &&
        !!values.LOCAL_LAST_NAME
      );
    }
    else if(userInfo?.userAccountDetails?.country === 'JP'){
      return !(
        !submitting &&
        (userInfo.userAccountDetails.termsAndConditions ? userInfo.userAccountDetails.termsAndConditions : tncData) &&
        !!values.FIRST_NAME &&
        !!values.LAST_NAME &&
        !!values.PHONE && (phoneNumberValidation(values.PHONE, data?.accountSetup?.countryCodeDropDown || [])) &&
        !!values.LOCAL_FIRST_NAME &&
        !!values.LOCAL_LAST_NAME &&
        !!(userInfo?.userAccountDetails?.department || values.DEPARTMENT) &&
        !!(userInfo?.userAccountDetails?.role || values.ROLE)
      );
    } else {
      return !(
        !submitting &&
        !!(userInfo?.userAccountDetails.department || values.DEPARTMENT) &&
        !!(userInfo?.userAccountDetails.role || values.ROLE) &&
        (userInfo?.userAccountDetails.termsAndConditions ? userInfo.userAccountDetails.termsAndConditions : tncData) &&
        !!values.FIRST_NAME &&
        !!values.LAST_NAME &&
        !!values.PHONE && (phoneNumberValidation(values.PHONE, data?.accountSetup?.countryCodeDropDown || []))
      );
    }
  };

  if (isLoading) {
    return null;
  }

  const changeHandler=()=>{
    setTncData(!tncData);
  }


  return (
    <>
      {data?.metadata && <Metadata metadata={data.metadata} />}
      <LoadableSection loading={isLoadingUserInfo || isLoadingProfessionalData} error={false}>{
        ()=>{
          return <>
           <div className="person-header">
        <div className="logo">
          <Logo
            className="logo"
            baseUrl="/"
            logoImage={convertToFullBaseUrl(data?.accountSetup?.logoLink?.logoPath)}
            logoLink={currentDomain}
            isEditing={false}
            parentComponent={''}
            siteCDNLogo={convertToFullBaseUrl(data?.accountSetup?.logoLink?.siteCDNLogo || '')}
          />
        </div>
        <div className="title">{data?.pageTitle}</div>
        <div />
      </div>
      <div>
        {setupErrorBar && (
          <Snackbar
            showCloseButton
            type="danger"
            onCloseClick={() => {
              setAddError();
            }}
            closeButtonIcon="ico-remove-16"
          >
            <RichText text={data?.accountSetup?.submitValidationMessage} tag="span" />
          </Snackbar>
        )}
      </div>
      <GridContainer className={baseClassName}>
        <GridRow>
          <GridCell desktop={8} tablet={8} phone={4}>
            <RichText text={data?.accountSetup?.setuptitle} className="description" />
          </GridCell>
        </GridRow>
        <GridRow>
          <GridCell desktop={8} tablet={8} phone={4}>
            <Form
              className="ge-registration-form ge-one-registration-form"
              enableDevTools={true}
              submitHandler={(e: any) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <FormConnector>
                {({ form: { values } }) => {
                  return (
                    <>
                      <IntroduceYourself
                        {...data.accountSetup}
                        sourceApplication={sourceApplication}
                        defaultValues={defaultValues}
                        websiteCountryCode={userMailingCountry}
                        globalCountry=""
                        preferredCountryCode=""
                        onCountryChange={(newCountryCode:string) => setIsdCode(newCountryCode)}
                      />
                     {(!(getLocaleFromHostname() === 'ja-jp' && userInfo?.userAccountDetails?.facilityType === VALUE_STRINGS.DEALER)) && ( 
                      <WhatDoYouDoContainer
                        defaultValues={{
                          DEPARTMENT: defaultValues.DEPARTMENT,
                          ROLE: defaultValues.ROLE,
                        }}
                        isFormValid={true}
                        submitState={{ status: 'submitted' }}
                        websiteCountryCode=""
                        whatDoYouDoCaption={data?.accountSetup?.whatDoYouDoCaption}
                        roleTitle={data?.accountSetup?.roleTitle}
                        roleCategories={data?.accountSetup?.roleCategories}
                        roleProfessionOptional={data?.accountSetup?.roleProfessionOptional}
                        jobTitleRequiredValidationMessage={data?.accountSetup?.jobTitleRequiredValidationMessage}
                        roleProfessionLabel={data?.accountSetup?.roleProfessionLabel}
                        roleProfessionOptionalRequiredLabel={data?.accountSetup?.roleProfessionOptionalRequiredLabel}
                        departmentLabel={data?.accountSetup?.departmentLabel}
                        roleProfessionPlaceholder={data?.accountSetup?.roleProfessionPlaceholder}
                        roleProfessionSelectItems={data?.accountSetup?.roleProfessionSelectItems}
                        departmentOptionalRequiredLabel={data?.accountSetup?.departmentOptionalRequiredLabel}
                        departmentPlaceholder={data?.accountSetup?.departmentPlaceholder}
                        departmentSelectItems={data?.accountSetup?.departmentSelectItems}
                        departmentOptional={data?.accountSetup?.departmentOptional}
                        departmentRequiredValidationMessage={data?.accountSetup?.departmentRequiredValidationMessage}
                        selectedFacilityType={selectedFacilityTypeValue}
                        setSelectedFacilityType={setSelectedFacilityTypeValue}
                      />)}
                      {!hideTnc && <TermsAndConsent {...data.accountSetup} defaultValues={defaultValues.TERMS_AND_CONDITIONS} changeHandler={changeHandler} />}
                      <GeButton
                        type="submit"
                        btnStyleType="solid-primary"
                        btnSize="large"
                        btnLabel={data?.accountSetup.submitButtonLabel}
                        onClick={() => handleSubmit(values)}
                        showLoadingSpinner={submitting}
                        disabled={checkConditionHandler(values) || isDisable}
                      />
                    </>
                  );
                }}
              </FormConnector>
            </Form>
          </GridCell>
        </GridRow>
      </GridContainer>
          </>
        }}
      </LoadableSection>
     
    </>
  );
};

export default SetupAccount;
