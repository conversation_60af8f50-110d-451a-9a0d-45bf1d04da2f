import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { ServiceNotificationsGroupList } from '../Component';
import { ServiceNotificationsGroupItemProps } from '../models';

// Add mocks for RichText and Checkbox components
jest.mock('cx-dle-component-library', () => ({
  RichText: ({ text, className, tag: Tag = 'div' }: { text: string; className?: string; tag?: string }) =>
    React.createElement(Tag, { className, 'data-testid': 'rich-text' }, text),
  Checkbox: ({
    children,
    value,
    name,
    className,
    changeHandler,
    ...props
  }: {
    children: React.ReactNode;
    value: boolean;
    name: string;
    className: string;
    changeHandler: (event: { target: { checked: boolean } }) => void;
  }) => (
    <div className={className}>
      <input type="checkbox" checked={value} name={name} onChange={changeHandler} {...props} />
      {children}
    </div>
  ),
}));

describe('ServiceNotificationsGroupList', () => {
  const mockOnChange = jest.fn();
  const defaultProps: ServiceNotificationsGroupItemProps = {
    onChange: mockOnChange,
    channelName: 'email',
    groupTitle: 'Unplanned',
    settings: [
      {
        appName: 'Equipment',
        code: 'TEST_CODE',
        groupName: 'group1',
        title: 'Title 1',
        description: 'Description 1',
        emailPreferenceDescription: 'Email Description',
        richDescription: 'Rich Description',
        isChecked: true,
        disableSelection: false,
        channelType: 'EMAIL',
        name: 'preference1',
        hasAccess: true,
      },
    ],
  };

  const renderComponent = (props = {}) => {
    return render(<ServiceNotificationsGroupList {...defaultProps} {...props} />);
  };

  it('renders list items correctly', () => {
    renderComponent();
    expect(screen.getByRole('list')).toBeInTheDocument();
    expect(screen.getByRole('listitem')).toBeInTheDocument();
  });

  it('handles checkbox change correctly', () => {
    render(<ServiceNotificationsGroupList {...defaultProps} />);
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);
    expect(mockOnChange).toHaveBeenCalledWith('TEST_CODE', false);
  });

  it('applies correct className based on disableSelection', () => {
    const propsWithDisabled = {
      ...defaultProps,
      settings: [
        {
          ...defaultProps.settings[0],
          disableSelection: true,
        },
      ],
    };
    const { container } = render(<ServiceNotificationsGroupList {...propsWithDisabled} />);
    expect(container.querySelector('.service-notification-group-List__hide')).toBeInTheDocument();
  });

  it('displays correct description based on channel type', () => {
    render(<ServiceNotificationsGroupList {...defaultProps} />);
    expect(screen.getByText('Email Description')).toBeInTheDocument();
  });

  it('displays richDescription when description is empty', () => {
    renderComponent({
      settings: [
        {
          ...defaultProps.settings[0],
          description: '',
        },
      ],
    });
    expect(screen.getByText('Rich Description')).toBeInTheDocument();
  });

  it('renders multiple settings items', () => {
    renderComponent({
      settings: [
        defaultProps.settings[0],
        {
          ...defaultProps.settings[0],
          code: 'code2',
          title: 'Title 2',
        },
      ],
    });
    expect(screen.getAllByRole('listitem')).toHaveLength(2);
  });

  it('sets correct checkbox value based on channelType and isChecked', () => {
    render(<ServiceNotificationsGroupList {...defaultProps} />);
    const checkbox = screen.getByRole('checkbox') as HTMLInputElement;
    expect(checkbox.checked).toBe(true);
  });
});
