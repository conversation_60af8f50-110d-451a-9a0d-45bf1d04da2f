@import '~cx-dle-component-library/styles/core/functions';
@import '~cx-dle-component-library/styles/core/variables';
@import '~cx-dle-component-library/styles/core/mixins';

.ge-breadcrumb-nav {
  &-wrapper {
    width: 100%;
    display: block;
    border-bottom: 1px solid $background-100;
    background: $background;
    box-shadow: 0 4px 8px -4px rgba(0, 0, 0, 0.2);
  }

  &-wrapper-support {
    width: 100%;
    display: block;
    border-bottom: 1px solid $background-100;
    background: $background;
    box-shadow: 0 10px 30px #1629410d;
  
    .secondary-navbar__item {
      font-family: "Source Sans Pro";
      font-size: 16px;

      .title {
        color: #222222;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }

  &-container {
    width: 100%;
    padding: 16px;

    @include desktop {
      padding: 18px;
    }
  }

  &-container-support {
    width: 100%;
    padding: 20px;
    height: 64px;

    @include desktop {
      padding: 18px;
    }
  }
}