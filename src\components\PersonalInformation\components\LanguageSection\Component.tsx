import { FormValues } from 'cx-dle-common-lib';
import { FORM_FIELDS } from './constants';
import { InformationFormField, LanguageSectionProps } from './models';
import { UpdateUserProfileFormControl } from '../../../Common/UpdateUserProfileForm/Component';

export const LanguageSection = ({
  language,
  languageTitle,
  languageDescription,
  updateConfirmationMessage,
  formName,
  subFormName,
  ...lanuageRestProps
}: LanguageSectionProps) => {
  const options = lanuageRestProps.newLanguages.map(({ fields: { name, title } }: any) => ({
    label: name,
    value: title?.value,
  }));

  const optionselected = lanuageRestProps.newLanguages.map(({ fields: { name, title, value } }: any) => ({
    label: name,
    title: title?.value,
    value: value?.value,
  }));

  const currentLanguage = optionselected.find((option: any) => option?.value === language) || '';
  const formFieldsConfiguration: InformationFormField[] = [
    {
      name: FORM_FIELDS.NEWLANGUAGE,
      options,
      requiredLabelText: lanuageRestProps.requiredLabelText,
      title: lanuageRestProps.newLanguageText,
      type: 'select',
      value: currentLanguage.title,
    },
  ];

  const getMutationArgs = (values: FormValues): any => ({
    language: options?.find((option: any) => option?.value === values[FORM_FIELDS.NEWLANGUAGE]?.value)?.value,
  });
  return (
    <>
      <UpdateUserProfileFormControl
        formName={formName}
        subFormName={subFormName}
        sectionTitle={languageTitle}
        sectionDescription={languageDescription}
        formFieldsConfiguration={formFieldsConfiguration as any}
        updateConfirmationMessage={updateConfirmationMessage}
        getMutationArgs={getMutationArgs}
        {...lanuageRestProps}
        refetch={async () => {
          await lanuageRestProps.refetch();
        }}
      >
        {currentLanguage && currentLanguage.label}
      </UpdateUserProfileFormControl>
    </>
  );
};
