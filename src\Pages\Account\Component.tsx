import { useQuery } from '@apollo/client';
import { ErrorMessage, Snackbar } from 'cx-dle-component-library';
import Capabilities from '../../components/Capabilities';
import { Metadata } from '../../components/Metadata';
import ProfileCard from '../../components/ProfileCard';
import { ProgressiveRegistration, useCommonPageData, usePageData, useUserInfo } from '../../hooks';
import { Account as AccountProps, ProfileInfoType, ServicesType } from '../../models/Account';
import { useTranslation } from 'react-i18next';
import { Dlesitecommoncontent } from '../../models/Dlesitecommoncontent';
import { getProgressiveRegistrations } from '../../components/Capabilities/query';

export const Account = () => {
  const { data, isLoading } = usePageData<AccountProps>('account', 'accountData');
  
  const { data: modalityData } = useCommonPageData<Dlesitecommoncontent>(
    'dlesitecommoncontent',
    'dlesitecommoncontentData',
  );
  
  const { data: userInfo, loading: userInfoLoading, error, setError } = useUserInfo();
  
  const { data: userProgressiveRegistrations, loading: progressiveRegistrationsLoading } = useQuery<
    ProgressiveRegistration[]
  >(getProgressiveRegistrations, { variables: { queryContext: { applicationId: '' } } });

  const { t } = useTranslation();

  if (isLoading) {
    return null;
  }

  return (
    <>
      {data?.metadata && <Metadata metadata={data.metadata} />}
      {error && (
        <Snackbar
          showCloseButton={true}
          type="danger"
          onCloseClick={() => {
            setError(false);
          }}
        >
          <ErrorMessage message={t('Errors.Default')} />
        </Snackbar>
      )}

      <ProfileCard
        userInfo={userInfo}
        userInfoLoading={userInfoLoading}
        {...(data?.profileInfo as ProfileInfoType)}
      />

      <Capabilities
        userInfo={userInfo}
        modalityData={modalityData}
        userProgressiveRegistrations={userProgressiveRegistrations}
        loading={progressiveRegistrationsLoading || userInfoLoading}
        {...(data?.services as ServicesType)}
      />
    </>
  );
};
