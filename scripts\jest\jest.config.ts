import type { Config } from '@jest/types';

const config: Config.InitialOptions = {
  verbose: true,
  rootDir: '../../',
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/scripts/jest/setup/jest.setup.ts'],
  moduleNameMapper: {
    '\\\\.(css|less|sass|scss)$': '<rootDir>/src/__mocks__/styleMock.js',
    '\\.(css|less|sass|scss)$': '<rootDir>/src/__mocks__/styleMock.js',
    '\\\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/scripts/jest/__mocks__/fileMock.js',
    '^cx-dle-component-library$': '<rootDir>/src/__mocks__/cx-dle-component-library.ts',
    '^cx-dle-common-lib$': '<rootDir>/src/__mocks__/cx-dle-common-lib.ts',
  },
  transform: {
    '^.+\\\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.json',
      },
    ],
    '^.+\\\\.js$': 'babel-jest',
  },
  transformIgnorePatterns: ['node_modules/(?!(cx-dle-common-lib|cx-dle-component-library)/)'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', 'scss', 'css'],
  testMatch: ['**/src/**/__tests__/**/*.test.[jt]s?(x)', '**/src/**/?(*.)+(spec|test).[jt]s?(x)'],
  testPathIgnorePatterns: ['/node_modules/', '/libs/'],
  moduleDirectories: ['node_modules', 'src'],
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageThreshold: {
    global: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0,
    },
  },
  coverageReporters: ['json', 'lcov', 'text', 'clover', 'cobertura'],

  // Sonar configuration
  testResultsProcessor: 'jest-sonar-reporter',
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'test-results',
        outputName: 'junit.xml',
      },
    ],
  ],
};

export default config;
