import { Cookies } from 'react-cookie';
import { jwtDecode } from 'jwt-decode';
import {
  renderFormFieldsByLocale,
  getWebSocketDownloadDocumentSettings,
  getDecodedCookieValue,
  getUserCountry,
  shouldShowComponent,
  getAnalyticsProps,
  checkApplicationAccess,
  getApplicationNamesByCountry,
  getApplicationNamesForLinksByCountry,
  getRegexFromString,
  decodeIdToken,
} from '../commonUtils';
import { AVAILABLE_COUNTRIES, ID_TOKEN_COOKIE, ID_TOKEN } from '../../constants';
import { OMNITURE_EVENTS } from 'cx-dle-common-lib';

// Mock dependencies
jest.mock('react-cookie');
jest.mock('jwt-decode');
jest.mock('../index', () => ({
  isCSMMode: jest.fn(),
  isEUSite: jest.fn(),
  isLASite: jest.fn(),
}));
jest.mock('../../config', () => ({
  endpoints: {
    documentProviderLatamWebSocketURI: 'ws://latam.example.com',
    documentProviderAfricaWebSocketURI: 'ws://africa.example.com',
    documentProviderWebSocketURI: 'ws://global.example.com',
    documentProviderTimeOut: 30000,
  },
}));

const mockCookies = Cookies as jest.MockedClass<typeof Cookies>;
const mockJwtDecode = jwtDecode as jest.MockedFunction<typeof jwtDecode>;
const { isCSMMode, isEUSite, isLASite } = require('../index');

// Mock window and document
const mockWindow = {
  document: {
    cookie: 'test-cookie=value',
  },
};

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true,
});

Object.defineProperty(global, 'atob', {
  value: jest.fn((str) => Buffer.from(str, 'base64').toString('binary')),
  writable: true,
});

describe('commonUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('renderFormFieldsByLocale', () => {
    const mockFormFields = [
      { key: 'field1' },
      { key: 'field2' },
      { key: 'field3' },
    ];

    const mockFieldOrderMap = {
      field1: { US: 0, CA: 1, GLOBAL: 0 },
      field2: { US: 1, CA: 0, GLOBAL: 1 },
      field3: { US: -1, CA: 2, GLOBAL: 2 },
    };

    it('returns original form fields for GLOBAL country code', () => {
      const result = renderFormFieldsByLocale(mockFormFields, 'GLOBAL', mockFieldOrderMap);
      expect(result).toEqual(mockFormFields);
    });

    it('defaults to US when country code is not available', () => {
      const result = renderFormFieldsByLocale([...mockFormFields], 'INVALID', mockFieldOrderMap);
      expect(result).toBeDefined();
      expect(result.length).toBe(2); // field3 should be removed for US (-1)
    });

    it('reorders fields according to field order map', () => {
      const result = renderFormFieldsByLocale([...mockFormFields], 'CA', mockFieldOrderMap);
      expect(result).toBeDefined();
      expect(result.length).toBe(3); // All fields should be present for CA
    });

    it('removes fields with -1 index', () => {
      const result = renderFormFieldsByLocale([...mockFormFields], 'US', mockFieldOrderMap);
      expect(result.length).toBe(2); // field3 should be removed
      expect(result.find((field: any) => field.key === 'field3')).toBeUndefined();
    });
  });

  describe('getWebSocketDownloadDocumentSettings', () => {
    it('returns LATAM WebSocket URI for LA site', () => {
      isLASite.mockReturnValue(true);
      isEUSite.mockReturnValue(false);

      const result = getWebSocketDownloadDocumentSettings();
      expect(result.webSocketUri).toBe('ws://latam.example.com');
      expect(result.timeout).toBe(30000);
    });

    it('returns Africa WebSocket URI for EU site', () => {
      isLASite.mockReturnValue(false);
      isEUSite.mockReturnValue(true);

      const result = getWebSocketDownloadDocumentSettings();
      expect(result.webSocketUri).toBe('ws://africa.example.com');
      expect(result.timeout).toBe(30000);
    });

    it('returns global WebSocket URI for other sites', () => {
      isLASite.mockReturnValue(false);
      isEUSite.mockReturnValue(false);

      const result = getWebSocketDownloadDocumentSettings();
      expect(result.webSocketUri).toBe('ws://global.example.com');
      expect(result.timeout).toBe(30000);
    });
  });

  describe('getDecodedCookieValue', () => {
    it('returns decoded cookie value when cookie exists', () => {
      const mockPayload = { user: 'test', exp: 1234567890 };
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('header.eyJ1c2VyIjoidGVzdCIsImV4cCI6MTIzNDU2Nzg5MH0.signature'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = getDecodedCookieValue('test-cookie');
      expect(result).toEqual(mockPayload);
    });

    it('returns null when cookie does not exist', () => {
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue(null),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = getDecodedCookieValue('non-existent-cookie');
      expect(result).toBeNull();
    });

    it('returns null when window is undefined', () => {
      const originalWindow = global.window;
      delete (global as any).window;

      const result = getDecodedCookieValue('test-cookie');
      expect(result).toBeNull();

      global.window = originalWindow;
    });
  });

  describe('getUserCountry', () => {
    it('returns user country from decoded token', () => {
      const mockPayload = { address: { country: 'US' } };
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('header.eyJhZGRyZXNzIjp7ImNvdW50cnkiOiJVUyJ9fQ.signature'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = getUserCountry();
      expect(result).toBe('US');
    });

    it('returns undefined when no country in token', () => {
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue(null),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = getUserCountry();
      expect(result).toBeUndefined();
    });
  });

  describe('shouldShowComponent', () => {
    it('returns false when supportedCountries is empty', () => {
      const result = shouldShowComponent([]);
      expect(result).toBe(false);
    });

    it('returns true when user country is in supported countries (non-CSM mode)', () => {
      isCSMMode.mockReturnValue(false);
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue('header.eyJhZGRyZXNzIjp7ImNvdW50cnkiOiJVUyJ9fQ.signature'),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);

      const result = shouldShowComponent(['US', 'CA']);
      expect(result).toBe(true);
    });

    it('returns true when country parameter matches in CSM mode', () => {
      isCSMMode.mockReturnValue(true);

      const result = shouldShowComponent(['US', 'CA'], 'US');
      expect(result).toBe(true);
    });

    it('returns false when country parameter does not match in CSM mode', () => {
      isCSMMode.mockReturnValue(true);

      const result = shouldShowComponent(['US', 'CA'], 'UK');
      expect(result).toBe(false);
    });
  });

  describe('getAnalyticsProps', () => {
    it('returns correct analytics properties', () => {
      const item = { title: 'Test Title', text: 'Test Text' };
      const result = getAnalyticsProps(item);

      expect(result).toEqual([
        { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
        { name: 'linkName', value: 'Test Text' },
        { name: 'linkType', value: 'profile settings' },
        { name: 'linkTitle', value: 'Test Title' },
      ]);
    });

    it('uses default title when not provided', () => {
      const item = { text: 'Test Text' };
      const result = getAnalyticsProps(item);

      expect(result[3]).toEqual({ name: 'linkTitle', value: 'profile information' });
    });
  });

  describe('checkApplicationAccess', () => {
    const assignedApplications = [
      { applicationName: 'App1' },
      { applicationName: 'App2' },
      { applicationName: 'App3' },
    ];

    it('returns true when user has access to at least one application', () => {
      const result = checkApplicationAccess(assignedApplications, ['App1', 'App4']);
      expect(result).toBe(true);
    });

    it('returns false when user has no access to any application', () => {
      const result = checkApplicationAccess(assignedApplications, ['App4', 'App5']);
      expect(result).toBe(false);
    });

    it('handles empty assigned applications', () => {
      const result = checkApplicationAccess([], ['App1']);
      expect(result).toBe(false);
    });

    it('handles empty applications to check', () => {
      const result = checkApplicationAccess(assignedApplications, []);
      expect(result).toBe(false);
    });
  });

  describe('getApplicationNamesByCountry', () => {
    const tabs = [
      {
        applicationAccess: [
          { countryCode: 'US', applicationName: 'App1' },
          { countryCode: 'CA', applicationName: 'App2' },
        ],
      },
      {
        applicationAccess: [
          { countryCode: 'US', applicationName: 'App3' },
        ],
      },
      {}, // Tab without applicationAccess
    ];

    it('returns application names for specific country', () => {
      const result = getApplicationNamesByCountry(tabs, 'US');
      expect(result).toEqual(['App1', 'App3']);
    });

    it('returns empty array for country with no applications', () => {
      const result = getApplicationNamesByCountry(tabs, 'UK');
      expect(result).toEqual([]);
    });

    it('handles empty tabs array', () => {
      const result = getApplicationNamesByCountry([], 'US');
      expect(result).toEqual([]);
    });
  });

  describe('getRegexFromString', () => {
    it('creates regex from string', () => {
      const result = getRegexFromString('test.*pattern');
      expect(result).toBeInstanceOf(RegExp);
      expect(result.source).toBe('test.*pattern');
    });
  });

  describe('decodeIdToken', () => {
    it('decodes ID token from cookies', () => {
      const mockToken = 'mock.jwt.token';
      const mockDecodedToken = { user: 'test', exp: 1234567890 };
      
      const mockCookieInstance = {
        get: jest.fn().mockReturnValue(mockToken),
      };
      mockCookies.mockImplementation(() => mockCookieInstance as any);
      mockJwtDecode.mockReturnValue(mockDecodedToken);

      const result = decodeIdToken();
      expect(mockJwtDecode).toHaveBeenCalledWith(mockToken);
      expect(result).toEqual(mockDecodedToken);
    });
  });
});
