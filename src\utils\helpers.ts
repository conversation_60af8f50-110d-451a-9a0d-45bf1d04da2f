import { AUTHENTICATION, CSM_ADMIN_COOKIE_NAME, CSM_MICRO_APP_COOKIE, GENERAL_COOKIES, localGetJwtPayload, Utils } from 'cx-dle-common-lib';
import config from '../config';
import { ACCOUNT_MICROAPP, CSM_ACCESS_COOKIE_NAME, CSM_ADMIN_LEVEL_READWRITE, CSM_READ_ONLY_ACCESS, CSM_USER_DATA_STORAGE_KEY, currentDomain } from '../constants';
import { Cookies } from 'react-cookie';
import { getLocaleInfoFromHostName } from './localeMapper';
import moment from 'moment';
import { useRedirect } from '../hooks';
import { LinkType, LocaleConfig } from '../models/base/common';
import { TimelineLocalizationData } from '../components/ActivityTimeline/models';

export const getReturnUrl = (returnUrl: string): string => {
    const redirectUrl = `${currentDomain}${returnUrl}${location.search}`;
    return redirectUrl;
}

const GetCSMUserDetails = () => {
    try {
        const impersonatedUserData = JSON.parse(localStorage.getItem(CSM_USER_DATA_STORAGE_KEY) as string);
        return impersonatedUserData?.user;
    } catch (error) {
        console.error('Unable to get CSM User Details', error);
        return null;
    }
};

export const getAuthRedirectUrl = (authUrl: string, returnUrl: string): string => {
    // returnUrl will be relative url starting with /
    if (!returnUrl || !returnUrl?.startsWith('/')) {
        return authUrl;
    }

    const urlFragments = authUrl?.split('?');
    const queryParams = urlFragments?.[1] ?? {};
    const searchParams = new URLSearchParams(queryParams);
    searchParams.set('returnUrl', returnUrl?.trim() === '/' ? returnUrl : getReturnUrl(returnUrl));
    return (urlFragments?.[0] ?? '') + '?' + searchParams.toString();
}


export const getSFDCLogInUrl = (): string => {
    const loginUrl = config?.sfdcLoginUrl?.trim();

    if (loginUrl) {
        return loginUrl?.startsWith('#') ? ACCOUNT_MICROAPP?.SFDC_LOGIN_URL : loginUrl;
    }

    return ACCOUNT_MICROAPP?.SFDC_LOGIN_URL;
}

export const getSFDCLogOutUrl = (): string => {
    const logoutUrl = config?.sfdcLogoutUrl?.trim();
    const csmUserDetails = GetCSMUserDetails();

    // If CSM user details are found in the localstorage, then logout using monolith endpoint
    // until ID service supports CSM logout completely
    if (csmUserDetails?.userID) {
        return ACCOUNT_MICROAPP?.SFDC_LOGOUT_URL;
    }

    if (logoutUrl) {
        return logoutUrl?.startsWith('#') ? ACCOUNT_MICROAPP?.SFDC_LOGOUT_URL : logoutUrl;
    }

    return ACCOUNT_MICROAPP?.SFDC_LOGOUT_URL;
}


export const isCSMMode = (): boolean => {
  if (typeof window !== 'undefined') {
    const cookies = new Cookies(window.document.cookie);
    return cookies.get(CSM_ADMIN_COOKIE_NAME) && (cookies.get(CSM_ADMIN_COOKIE_NAME) === 'true' || cookies.get(CSM_ADMIN_COOKIE_NAME) === true);
  }
  return false;
};

export const CSMRole = () => {
  if (typeof window !== 'undefined') {
    const cookies = new Cookies(window.document.cookie);
    const csmRole = cookies.get(CSM_ACCESS_COOKIE_NAME);
    return csmRole;
  }
  return null;
};

export const isCSMReadOnly = () => {
  if (isCSMMode()) {
    const role = CSMRole();
    return role === CSM_READ_ONLY_ACCESS;
  }
  return false;
};

export const isCSMAdminMode = () => {
  if (typeof window !== 'undefined') {
    const cookies = new Cookies(window.document.cookie);
    return isCSMMode() && cookies.get(CSM_ACCESS_COOKIE_NAME) === CSM_ADMIN_LEVEL_READWRITE;
  }
  return false;
};
  
export default function getUserDetailsFromCookies() {
  if (isCSMMode()) {
    const impersonatedUserData = typeof localStorage !== 'undefined' ? JSON.parse(localStorage?.getItem(CSM_USER_DATA_STORAGE_KEY) || '') : null;
    return impersonatedUserData?.user;
  } else {
    const cookies = new Cookies(window.document.cookie);
    const idToken = cookies.get(AUTHENTICATION.idTokenCookie);
    return localGetJwtPayload(idToken as string);
  }
}

export const convertToFullBaseUrl = (relativeUrl: string): string => {
  // If relativeUrl already contains protocol, it means it is already a full base url
  if (relativeUrl?.includes('http')) {
    return relativeUrl;
  }

  // Prepend slash if relativeUrl does not already contain that
  if (!relativeUrl.startsWith('/')) {
    return currentDomain + '/' + relativeUrl;
  }

  return currentDomain + relativeUrl;
};

export const isEUSite = (): boolean => {
  const localeInfo = getLocaleInfoFromHostName();
  if (localeInfo.apiGatewayRegion == 'EU') {
    return true;
  }
  return false;
}

export const isLASite = (): boolean => {
  const localeInfo = getLocaleInfoFromHostName();
  if (localeInfo.apiGatewayRegion == 'LA') {
    return true;
  }
  return false;
}

export const isAuthenticated = (): boolean => {
  const menuId = Utils.getCookie(GENERAL_COOKIES.menuId);
  const hasCsmAccess = Utils.getCookie(CSM_ADMIN_COOKIE_NAME);
  const csmDetails = Utils.getCookie(CSM_MICRO_APP_COOKIE);
  const impersonationDetails = localStorage.getItem(CSM_USER_DATA_STORAGE_KEY);
  const accessToken = Utils.getCookie(AUTHENTICATION.accessTokenCookie);
  const idToken = Utils.getCookie(AUTHENTICATION.idTokenCookie);
  const authType = Utils.getCookie(AUTHENTICATION.authTypeCookie);

  if (!(idToken && accessToken && authType)) {
    return false;
  }

  const tokenPayload = localGetJwtPayload(idToken);
  const expDateTime = moment(tokenPayload.exp, 'X');
  const now = moment().add(AUTHENTICATION.tokenExpirationDelta, 'milliseconds');
  const redirect = useRedirect();

  if (authType && authType === 'sfdc') {
    if (menuId) {
      if (!menuId && hasCsmAccess && hasCsmAccess === 'false') {
        throw new Error();
      } else if (!menuId && !hasCsmAccess) {
        throw new Error();
      } else if (!csmDetails && hasCsmAccess && hasCsmAccess === 'true' && !impersonationDetails) {
        redirect(config.errorPages.error403);
        return true;
      }
    }
  }

  if (authType && authType === 'sso') {
    const isRefresh = !!Utils.getCookie(AUTHENTICATION.accessTokenCookie) && !!Utils.getCookie(AUTHENTICATION.idTokenCookie);
    return isRefresh;
  } else {
    return now < expDateTime;
  }

};
 
export const getUserSpecificRedirectUrl = (redirectLocale: string) => {
  for (const locale in config.locales) {
    const localeConfig: LocaleConfig = config.locales[locale as keyof typeof config.locales];
    const configrationExists = localeConfig.localeCode === redirectLocale?.toLowerCase();
    if (configrationExists) {
      return `${window.location.protocol}//${localeConfig.hostname}`;
    }
  }
  return `${window.location.protocol}//${config.locales.us.hostname}`;
};

export const getUserSpecificLink = (links: LinkType[], hostedCountry: string, userMailingCountry = '') => {
  if (!links?.length) return links as unknown as LinkType;

  // Get user country
  const user = getUserDetailsFromCookies();

  let userCountry = user?.address?.country || 'US';
  if(userMailingCountry){
    userCountry = userMailingCountry;
  }
  // Loop through the links and find by user country or hosted country
  const userSpecificLink =
    links.find((l) => l.countryCode === userCountry) || links.find((l) => l.countryCode === hostedCountry) || links[0];

  if (userSpecificLink.href.includes('http')) {
    return {
      ...userSpecificLink,
      href: userSpecificLink.href,
      url: userSpecificLink.url,
    };
  }
  
  return {
    ...userSpecificLink,
    href: `${getUserSpecificRedirectUrl(userSpecificLink.redirectLocale)}${userSpecificLink.href}`,
    url: `${getUserSpecificRedirectUrl(userSpecificLink.redirectLocale)}${userSpecificLink.url}`,
  };
};

export const getLocalizedModality = (
  timelineLocalizationData: TimelineLocalizationData,
  modalityId: string | number
) => {
  const { modalityLocalization } = timelineLocalizationData;
  return (
    modalityLocalization?.find(
      (modality: any) => modality.modalityId === modalityId.toString()
    ) || ""
  );
};

export const getIdentityService = () => {
  const identityServiceValue = config?.useIdentityServiceUrl?.trim();

  if(identityServiceValue === "true") return true
  return false
}