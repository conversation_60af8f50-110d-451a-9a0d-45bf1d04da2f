import { EDSIcon } from 'cx-dle-component-library';
import SkeletonLoader from '../../Common/SkeletonLoader';
import { Icon } from 'cx-dle-component-library/components/EDS/models';
import { prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import parse from 'html-react-parser';
import { CardInfoProps } from './models';
import { getAnalyticsProps, shouldShowComponent } from '../../../utils/commonUtils';
import { ProfileCardIcons } from '../../../constants';
import classNames from 'classnames';

export const CardInfo = ({
  showSkeletonLoader,
  profileInfo,
  organization,
  cardType,
  showCommunicationPreferences,
  props,
  hasNotification,
  notificationCount,
  showNotificationCard,
}: CardInfoProps) => {
  const cardData = props?.[`${cardType}Card`];
  const userMailingCountry = profileInfo?.country;

  if (!shouldShowComponent(cardData?.supportedCountries, userMailingCountry)) return null;

  return (
    <div className="card-user-info">
      {(cardType !== 'notification' || (cardType === 'notification' && showNotificationCard)) && (
        <div className= {classNames({"card-img-wrap": cardType !== "profile"})}>
          <EDSIcon className="card-avatar-icon" icon={ProfileCardIcons[cardType] as Icon} />
          {hasNotification && <span className="card-avatar-icon__active" />}
        </div>
      )}

      
        {showSkeletonLoader ? (
          <SkeletonLoader list={[{ rows: Array(3).fill({ className: 'card-user-details__loading top-header-loader' }) }]} />
        ) : (
          <div className="card-user-details">
            {/* Render Profile Card */}
            {cardType === 'profile' && (
              <>
                <div className='card-info-wrapper'>
                  <div className="card-user-name">{`${profileInfo?.firstName || ''} ${
                    profileInfo?.lastName || ''
                  }`}</div>
                  <div className="card-description__role">{profileInfo?.role}</div>
                  {organization && <div className="card-description">{organization}</div>}
                </div>
                {renderLinks([cardData?.manageProfileLink], userMailingCountry)}
                {showCommunicationPreferences && renderLinks([cardData?.communicationsPreferencesLink], userMailingCountry)}
              </>
            )}

            {/* Render Help Card */}
            {cardType === 'help' && (
              <>
                <div className='card-info-wrapper'>
                  <div className="card-need-info">{cardData?.title}</div>
                  <div className="card-description__role hide-mobile">{cardData?.description}</div>
                </div>
                {profileInfo?.isHoldingAccount ? null : renderLinks([cardData?.contactTeamLink], userMailingCountry)}
                {renderLinks([cardData?.supportHomeLink], userMailingCountry)}
              </>
            )}

            {/* Render Notification Card */}
            {cardType === 'notification' && showNotificationCard && (
              <div>
                <div className='card-info-wrapper'>
                  <div className="card-notification-title">{cardData?.title}</div>
                  <div className="card-description__role">
                    {parse(cardData?.description?.replace(`{count}`, `<span>${notificationCount}</span>`))}
                  </div>
                </div>
                {renderLinks([cardData?.notificationViewLink], userMailingCountry)}
              </div>
            )}
          </div>
        )}
    </div>
  );
};

//Utility function to dynamically render links based on visibility.
const renderLinks = (links: any[], userMailingCountry: string) =>
  links.map(
    (link, index) =>
      shouldShowComponent(link?.supportedCountries, userMailingCountry) && (
        <div key={index} className="card-user-details__link-section no-top-padding">
          <a href={link?.href} {...prepareDataAnalyticsAttributes(getAnalyticsProps(link))}>
            {link?.text}
          </a>
        </div>
      ),
  );
