import * as React from 'react';
import { prepareDataAnalyticsAttributes, dispatchEvent, FormElement } from 'cx-dle-common-lib';
import { TextInput } from 'cx-dle-component-library';

import { GeTextInputProps } from './models';

import './styles.scss';

export const GeTextInput = ({
  name,
  validate,
  defaultValue,
  inputMode,
  hintText,
  disabled,
  labelText,
  requiredText,
  ...props
}: GeTextInputProps) => {
  const inputRef: React.RefObject<HTMLInputElement> = React.createRef();

  return (
    <FormElement name={name} validate={validate} defaultValue={defaultValue}>
      {({ handleChange, handleBlur, handleFocus, errors, reset, touched, focused, values, valid, status, changed }) => {
        const isChanged = changed[name];
        const isError = props.isError || (touched[name] && !valid[name]);
        const isFocused = focused[name];
        const currentValue = values[name] !== undefined ? values[name] : (touched[name] ? '' : (defaultValue || ''));
        const isDirty = !!currentValue && !status.submitting; 
        const errorMessage = errors[name] && errors[name].length ? errors[name][0] : null;
        const formMessage = isError ? errorMessage : (hintText || null);
        const lastFieldChanged = status.lastFieldChanged === name;

        return (
          <TextInput
            name={name}
            textInputType={props.type ?? 'text'}
            textInputDisabled={disabled || status.submitting}
            value={currentValue}
            handleChange={handleChange}
            handleBlur={handleBlur}
            handleFocus={handleFocus}
            inputRef={inputRef}
            handleTextClearClick={(event) => {
              handleChange({ target: { name, value: '' } });
              handleBlur({ target: { name } });
              reset();
              dispatchEvent({ element: event.target as HTMLElement, eventType: 'input' });
              if (!inputRef.current) {
                return;
              }
              inputRef.current.focus();
            }}
            inputMode={inputMode}
            isTextInputError={isError}
            isFocused={isFocused}
            isDirty={isDirty}
            formMessage={formMessage}
            labelText={labelText}
            requiredText={requiredText}
            btnIcon="ico-remove-16"
            {...prepareDataAnalyticsAttributes([
              {
                name: 'errorMessage',
                value: errorMessage ? errorMessage : '',
              },
              {
                name: 'inputName',
                value: labelText || '',
              },
              {
                name: 'inputChanged',
                value: isChanged,
              },
              {
                name: 'lastFieldInteracted',
                value: lastFieldChanged,
              },
            ])}
            {...props}
          />
        );
      }}
    </FormElement>
  );
};
