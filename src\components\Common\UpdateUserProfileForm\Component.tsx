import * as React from 'react';
import { useMutation } from '@apollo/client';
import { ErrorMessage, Snackbar } from 'cx-dle-component-library';
import { InformationCard } from '../InformationCard/Component';
import { PreferencesCardContent } from '../PreferencesCard/components';
import { t } from 'i18next';
import { InformationEditForm } from '../InformationEditForm/Component';
import { FormValues, createGraphQLContext, FormSubmitTrackingData } from 'cx-dle-common-lib';
import { isCSMAdminMode, isCSMMode } from '../../../utils';
import { UpdateUserProfileFormProps } from './models';
import { UpdateUserProfile } from '../../ProfessionalInformation/model';
import { updateUserInfo } from './mutation';
import { PHONE } from '../../../constants';

export const UpdateUserProfileFormControl = ({
  getMutationArgs,
  refetch,
  cancelButtonText,
  updateConfirmationMessage,
  closeButtonText,
  editButtonText,
  formFieldsConfiguration,
  name,
  saveButtonText,
  sectionDescription,
  sectionTitle,
  sectionStatusInfo,
  successAlertType = 'success',
  defaultHideToggle,
  children,
  handleCancel,
  formName,
  subFormName,
  countryCodeDropDown,
  siteName,
  userInfo,
  id,
  extension,
  sectionExtensionTitle,
  filteredCountryList,
  validatedCountries,
  ...props
}: UpdateUserProfileFormProps) => {
  const [CSMErrorBar, setCSMErrorBar] = React.useState(false);
  const [isSubmitting, setSubmitting] = React.useState(false);
  const [alert, setAlert] = React.useState<any>({ text: '', type: '' });
  const [iserror, setError] = React.useState(false);
  const [errormessage, setErrorMessage] = React.useState<any>({ text: '', type: '' });

  const [updateUserProfileMutation] = useMutation<UpdateUserProfile.Mutation, UpdateUserProfile.Variables>(
    updateUserInfo,
    {
      context: createGraphQLContext<FormSubmitTrackingData>({
        subFormName,
        formName,
      }),
      onError: () => handleMutationError, // showErrorSnackbar, TODO
    },
  );

  const handleMutationCompleted = (data: any, updateToggle: () => void) => {
    setSubmitting(false);
    setError(false);
    updateToggle();
    const message = data?.updateUserProfile?.message || 'Profile updated successfully';
    setAlert({ text: message, type: successAlertType });
    refetch();
    setTimeout(() => {
      setAlert({ text: '', type: '' });
    }, 5000);
  };

  const handleMutationError = () => {
    setSubmitting(false);
    setError(true);
    const message = '<strong> Error:</strong>  {0}  Something went wrong';
    setErrorMessage({ text: message, type: 'danger' });
    setTimeout(() => {
      setErrorMessage({ text: '', type: '' });
      setError(false);
    }, 5000);
  };

  const handleSubmit = async (
    values: FormValues,
    getMutationArgs: ((values: FormValues) => {}) | undefined,
    setCSMErrorBar: (value: React.SetStateAction<boolean>) => void,
    updateToggle: () => void,
  ) => {
    setSubmitting(true);
    if (!getMutationArgs) {
      return;
    }

    const args = getMutationArgs(values);

    if (typeof window !== 'undefined') {
      if (isCSMMode() && !isCSMAdminMode()) {
        setCSMErrorBar(true);
        setSubmitting(false);
        return;
      }
    }

    updateUserProfileMutation({
      variables: { mutationArguments: args },
      onCompleted: (data) => handleMutationCompleted(data, updateToggle),
      onError: () => handleMutationError(),
    });
  };

  return (
    <>
      <div>
        {CSMErrorBar && (
          <Snackbar
            type="danger"
            onCloseClick={() => {
              setCSMErrorBar(false);
            }}
            showCloseButton
          >
            <ErrorMessage message={t('CSM.UserFriendlyMessage')} />
          </Snackbar>
        )}
      </div>
      <PreferencesCardContent
        className="personal-information__content"
        analyticsAttributes={[
          { name: 'subFormName', value: subFormName },
          { name: 'linkType', value: sectionTitle },
          { name: 'linkTitle', value: name as string },
        ]}
        closeLinkText={closeButtonText}
        editLinkText={editButtonText}
        wrapItems={true}
        defaultHideToggle={defaultHideToggle}
        shortContent={
          <>
            <InformationCard
              title={sectionTitle}
              alert={alert}
              updateConfirmationMessage={updateConfirmationMessage}
              statusInfo={sectionStatusInfo}
              onAlertCloseClick={() => setAlert({ text: '', type: '' })}
              iserror={iserror}
              errormessage={errormessage}
            >
              {children}
            </InformationCard>
            {id === PHONE && validatedCountries?.includes(filteredCountryList[0]?.countryCode) && (
              <InformationCard
                title={sectionExtensionTitle || ''}
                alert={alert}
                updateConfirmationMessage={updateConfirmationMessage}
                statusInfo={sectionStatusInfo}
                onAlertCloseClick={() => setAlert({ text: '', type: '' })}
                iserror={iserror}
                errormessage={errormessage}
                id={id}
              >
                {extension}
              </InformationCard>
            )}
          </>
        }
        expandedContent={(updateToggle) => (
          <InformationEditForm
            description={sectionDescription}
            saveButtonText={saveButtonText}
            cancelButtonText={cancelButtonText}
            fields={formFieldsConfiguration}
            isSubmitting={isSubmitting}
            handleSubmit={(values) => handleSubmit(values, getMutationArgs, setCSMErrorBar, updateToggle)}
            handleCancel={handleCancel}
            analyticsAttributes={[
              { name: 'subFormName', value: subFormName },
              { name: 'linkType', value: sectionTitle },
              { name: 'linkTitle', value: name as string },
            ]}
            countryCodeDropDown={(countryCodeDropDown as any) || []}
            siteName={siteName}
            countryCode={userInfo?.userAccountDetails?.country}
            filteredCountryList={filteredCountryList}
            validatedCountries={validatedCountries}
            {...props}
          />
        )}
      />
    </>
  );
};
