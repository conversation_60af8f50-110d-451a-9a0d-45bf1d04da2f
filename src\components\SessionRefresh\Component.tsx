import { useEffect } from 'react';
import { AuthenticationManager } from 'cx-dle-common-lib';
import { currentDomain } from '../../constants';
import { getIdentityService } from '../../utils';

interface PropsType {
  refreshCallback: () => Promise<void>;
}

export const SessionRefreshComponent = (props: PropsType) => {
  useEffect(() => {
    const refreshToken = async () => {
      const authManager = AuthenticationManager.getInstance();

      try {
        await authManager.refreshToken(getIdentityService(), currentDomain).then(() => props.refreshCallback());
      } catch (err) {
        authManager.redirectToLogin(false, currentDomain);
      }
    }

    refreshToken();
  }, []);

  return <></>;
};