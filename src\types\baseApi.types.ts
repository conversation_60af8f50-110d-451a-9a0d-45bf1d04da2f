import { IconType } from "../models/Sitecommoncontent";

export interface ApiError extends Error {
  statusCode?: number;
  response?: any;
}

export interface ApiConfig {
  baseURL: string;
  headers?: Record<string, string>;
  timeout?: number;
}

export interface ApiResponse<T> {
  data: T;
  status: number;
  headers: Record<string, string>;
}

export interface AuthenticationHeaders {
  [key: string]: string;
}

export interface SFDCNotificationFlagsLocalizationSettingDto {
  code: string;
  description: string;
  title: string;
  icon: IconType;
}
