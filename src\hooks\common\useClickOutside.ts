import { useEffect, useRef, useState } from 'react';

// Custom hook to detect outside click and set toggle state as false
// This hook can be used anywhere where in we would want to close any dropdown, modal
// by clicking outside except the elements within the ref element
export const useClickOutside = () => {
  const [toggle, setToggle] = useState(false);
  const wrapperRef = useRef<HTMLElement>(null);

  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      setToggle(false);
    }
  };

  const handleClickOutside = (event: Event) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setToggle(false);
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleEscape, true);
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('keydown', handleEscape, true);
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, [wrapperRef]);

  return { wrapperRef, toggle, setToggle };
};
