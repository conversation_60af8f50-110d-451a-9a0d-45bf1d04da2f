import classNames from 'classnames';
import { FormMessage } from 'cx-dle-component-library';
import { prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import { SelectDropdown } from 'cx-dle-component-library';
import { useEffect, useState } from 'react';
import './styles.scss';
import { FormElement } from 'cx-dle-common-lib';
import { GeSelectOption, GeSelectProps, SelectInputProps } from './model';
import { useClickOutside } from '../../../hooks/common/useClickOutside';

const SelectInput = ({
  className,
  disabled,
  formMessage,
  errorMessage,
  handleBlur,
  handleChange,
  isError,
  labelText,
  name,
  options,
  placeholder,
  requiredText,
  value,
  changed,
  lastFieldInteracted,
}: SelectInputProps) => {
  const { toggle, setToggle, wrapperRef } = useClickOutside();
  const [touched, setTouched] = useState(false);

  useEffect(() => {
    if (!toggle && touched) {
      handleBlur();
    }
    if (value) {
      if (!options.some((opt) => opt?.value === value?.value)) {
        handleChange(null);
      }
    }
  }, [toggle, value]);

  const handleButtonClick = (event: React.SyntheticEvent) => {
    event.preventDefault();
    setTouched(true);
    if (!disabled && !toggle) {
      setToggle(true);
    } else {
      handleBlur();
      setToggle(false);
    }
    handleChange(value?.value ? value : null);
  };

  const selectOption = async (option: GeSelectOption) => {
    handleChange(option);
    handleBlur();
    setToggle((t: boolean) => !t);
  };

  return (
    <div
      className={classNames('ge-material-select', { [className as string]: !!className })}
      {...prepareDataAnalyticsAttributes([
        {
          name: 'inputName',
          value: labelText || '',
        },
        {
          name: 'inputChanged',
          value: !!changed,
        },
        {
          name: 'lastFieldInteracted',
          value: !!lastFieldInteracted,
        },
        {
          name: 'errorMessage',
          value: errorMessage || '',
        },
      ])}
      ref={wrapperRef as any}
    >
      <SelectDropdown
        labelText={labelText}
        selectIconOpen={'ico-caret-up-16'}
        selectIconClose={'ico-caret-down-16'}
        toggle={toggle}
        options={options}
        isMulti={false}
        handleClick={handleButtonClick}
        value={value}
        placeholder={placeholder}
        handleChange={selectOption}
        handleClearOptions={() => {
          handleChange(null);
        }}
        className={className}
        name={name}
        isError={isError}
        disabled={disabled}
        requiredText={requiredText}
      />
      <FormMessage error={isError} message={formMessage || ''} />
    </div>
  );
};

export const GeSelect = ({
  name,
  validate,
  preserveState,
  defaultValue,
  hintText,
  labelText,
  options,
  placeholder,
  className,
  disabled,
  requiredText,
  ...props
}: GeSelectProps) => {
  return (
    <FormElement name={name} preserveState={preserveState} validate={validate} defaultValue={defaultValue}>
      {({ handleChange, handleBlur, handleFocus, touched, valid, errors, status, values, changed }) => {
        const isError = touched[name] && !valid[name];
        const errorMessage = errors[name] && errors[name].length ? errors[name][0] : null;
        const formMessage = isError ? errorMessage : hintText || null;
        return (
          <SelectInput
            {...props}
            className={isError ? 'form-label--error' : className}
            disabled={disabled}
            options={options}
            onChange={(option) => {
              if (props.onChange) {
                props.onChange(option, { values });
              }
            }}
            placeholder={placeholder}
            labelText={labelText}
            requiredText={requiredText}
            errorMessage={errorMessage}
            formMessage={formMessage}
            isError={isError}
            handleChange={handleChange}
            handleBlur={handleBlur}
            handleFocus={handleFocus}
            name={name}
            submitting={status.submitting}
            changed={changed[name]}
            lastFieldInteracted={status.lastFieldChanged === name}
            value={values[name]}
          />
        );
      }}
    </FormElement>
  );
};
