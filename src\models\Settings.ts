// This file is auto-generated. Do not edit manually.

export interface Settings {
  metadata: MetadataType;
  pageTitle: string;
  alertCloseIcon: string;
  breadcrumbProductName: string;
  breadcrumbBackIcon: string;
  breadcrumbSeparator: string;
  breadcrumbBackRedirectUrl: string;
  breadcrumbBackRedirectText: string;
  breadcrumb: BreadcrumbType;
  supportedCountriesForCommunications: string[];
  tabs: TabType[];
}

export interface MetadataType {
  browserTitle: string;
  metaAdditionalTags: MetaAdditionalTagType[];
  openGraphTags: OpenGraphTagType[];
  httpEquivalentTags: HttpEquivalentTagType[];
  linkTags: LinkTagType[];
}

export interface MetaAdditionalTagType {
  property: string;
  content: string;
}

export interface OpenGraphTagType {
  property: string;
  content: string;
}

export interface HttpEquivalentTagType {
  property: string;
  content: string;
}

export interface LinkTagType {
  rel: string;
  href: string;
  as: string;
}

export interface BreadcrumbType {
  isBreadcrumbEnabled: boolean;
  breadcrumbPageTitle: string;
  breadcrumbLink: BreadcrumbLinkType;
}

export interface BreadcrumbLinkType {
  href: string;
  text: string;
}

export interface TabType {
  id: string;
  name: string;
  tabTooltip: string;
  tabName: string;
  displayText: string;
  defaultActive: boolean;
}