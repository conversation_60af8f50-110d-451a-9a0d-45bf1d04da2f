import {
  AUTHENTICATION,
  Form,
  FormElement,
  OMNITURE_EVENTS,
  prepareDataAnalyticsAttributes,
  Utils,
  validationSchema,
} from 'cx-dle-common-lib';
import {
  FieldValidator,
  InformationEditFormProps,
  InformationFormField,
  InformationFormFieldType,
  UserCountry,
} from './models';
import { getValidatorOrFallback } from './utils';
import { useCallback, useEffect, useState } from 'react';
import { FormMessage, GeButton, PhoneExtensionInput, RichText } from 'cx-dle-component-library';
import { PhoneNumberWithCountryCodeV1 } from 'cx-dle-component-library';
import { CONTROL_KEYS, FORM_FIELDS, SITE_NAME, VALID_COUNTRYCODE } from '../../../constants';
import GeTextInput from '../GeTextInput';
import { useUserCountry } from '../../../hooks/common/useUserCountry';
import './styles.scss';
import GeSelect from '../GeSelect';
import { getRegexFromString } from '../../../utils/commonUtils';
import { FormConnector } from './FormConnector';

const spaceRegex = /\s/g;
const numericRegex = /\D/g;

export type { FieldValidator, InformationFormField, InformationFormFieldType };

export const InformationEditForm = ({
  analyticsAttributes,
  description,
  fields,
  saveButtonText,
  cancelButtonText,
  handleSubmit,
  handleCancel,
  countryCodeDropDown,
  siteName,
  isSubmitting,
  filteredCountryList,
  validatedCountries,
  countryCode,
}: InformationEditFormProps) => {
  const [country, setCountry] = useState('US');
  const [isPhoneNumberError, setIsPhoneNumberError] = useState(false);
  const [validCountry, setValidCountry] = useState(validatedCountries?.includes(filteredCountryList[0].countryCode));
  const idToken = Utils.getCookie(AUTHENTICATION.idTokenCookie);
  const tokenPayload = Utils.getJwtPayload(idToken as string) as any;
  const { data: userCountry } = useUserCountry<UserCountry>('country');

  const getCountry = async () => {
    if (tokenPayload?.address?.country) {
      setCountry(tokenPayload?.address?.country);
      return;
    }
    const sitename = siteName?.toLowerCase();
    if (sitename === SITE_NAME.AFRICA.toLowerCase()) {
      setCountry('ZA');
    } else if (sitename === SITE_NAME.FRANCE.toLowerCase()) {
      setCountry('FR');
    } else if (sitename === SITE_NAME.TUNISIA.toLowerCase()) {
      setCountry('TU');
    } else {
      setCountry(userCountry ? userCountry.data.websiteCountryCode : 'US');
    }
  };

  useEffect(() => {
    if (!country) {
      getCountry();
    }
  }, []);

  const getUserCountryDetails = useCallback(() => {
    const matchCountryDetails = countryCodeDropDown?.find((c) => c?.countryCode === countryCode);
    return matchCountryDetails || countryCodeDropDown?.find((c) => c?.countryCode === 'US');
  }, [countryCodeDropDown]);

  const submitHandler = async (formData: any, formHelpers: any) => {
    const { values } = formData;
    const { toggleSubmitting } = formHelpers;

    toggleSubmitting();
    try {
      await handleSubmit(values);
    } finally {
      toggleSubmitting();
    }
  };

  const handleCountryFlag = (value: any) => {
    if (value) {
      setValidCountry(VALID_COUNTRYCODE.includes(value.countryCode));
    }
  };

  return (
    <div className="information-edit-form">
      <div className="information-edit-form__row">
        <RichText className="information-edit-form__description" text={description} />
      </div>
      <Form
        className={validCountry ? `ge-form extension_added` : 'ge-form'}
        noValidate={true}
        submitHandler={submitHandler}
      >
        {fields.map((field, i) => {
          switch (field.type) {
            case 'select':
              // eslint-disable-next-line
              const filteredValue = field.options?.filter((i) => i.label === field.value) || [];
              return (
                <div key={i} className="information-edit-form__row">
                  <GeSelect
                    className="information-edit-form__field"
                    disabled={!!field.disabled}
                    labelText={field.title}
                    name={field.name}
                    defaultValue={field.options && field.options.find((option) => option.value === field.value)}
                    onChange={field.onChangeSelect}
                    options={field.options || []}
                    placeholder={field.placeholder}
                    requiredText={field.requiredMessageText ? field.requiredLabelText : ''}
                    validate={getValidatorOrFallback(
                      field.validateFunc,
                      field.requiredMessageText ? validationSchema().required(field.requiredMessageText) : null,
                    )}
                  />
                </div>
              );

            case 'email':
              return (
                <div key={i} className="information-edit-form__row">
                  <GeTextInput
                    className="information-edit-form__field"
                    disabled={!!field.disabled}
                    labelText={field.title}
                    name={field.name}
                    type={field.type}
                    defaultValue={field.value}
                    placeHolder={field.placeholder}
                    required={!!field.requiredMessageText}
                    requiredText={field.requiredLabelText}
                    hintText={field.hintText}
                    validate={getValidatorOrFallback(
                      field.validateFunc,
                      !!field.requiredMessageText && !!field.validationMessageText
                        ? validationSchema().required(field.requiredMessageText).email(field.validationMessageText)
                        : null,
                    )}
                  />
                </div>
              );

            case 'text':
            default: {
              if ((field.name === 'PHONE' || field.name === 'EXTENSION') && countryCodeDropDown?.length > 0) {
                return (
                  <div key={i} className="information-edit-form__row">
                    {field.name === 'PHONE' && (
                      <FormElement
                        name={field.name}
                        defaultValue={field.value || ''}
                        validate={getValidatorOrFallback(
                          field.validateFunc,
                          field.requiredMessageText ? validationSchema().required(field.requiredMessageText) : null,
                        )}
                      >
                        {({ handleChange, handleBlur, handleFocus, touched, valid }) => {
                          const isError = touched[field.name] && !valid[field.name];
                          const userCountry = getUserCountryDetails();
                          const val = field?.value?.replace(`${userCountry?.countryISDCode}`, '') || '';
                          return (
                            <>
                              <PhoneNumberWithCountryCodeV1
                                name={field.name}
                                defaultCountrySelected={userCountry ? userCountry.countryCode : country}
                                phoneNumberHelpText=""
                                dropdownLabel={field.title}
                                countryCodeDropDownList={countryCodeDropDown as any}
                                defaultPhoneNumber={val}
                                isPhoneNumberRequired={!!field.requiredMessageText}
                                handleChange={(v) => {
                                  const replacedVal = v
                                    ?.replace(`${userCountry?.countryISDCode}`, '')
                                    .replace(spaceRegex, '');
                                  const value = replacedVal?.replace(numericRegex, '');
                                  handleChange(value ? v : '');
                                }}
                                handlePhoneNumberError={(isError) => setIsPhoneNumberError(isError)}
                                handleBlur={handleBlur}
                                handleFocus={handleFocus}
                                className="information-edit-form__field"
                                maxLengthPhone="10"
                                placeHolder=""
                                requiredText={field.requiredLabelText}
                                handlerCountryListChange={(value: any) => handleCountryFlag(value)}
                              />
                              <FormMessage error={isError} message={isError && field.requiredMessageText} />
                            </>
                          );
                        }}
                      </FormElement>
                    )}

                    {validCountry && field.name === 'EXTENSION' && (
                      <FormElement
                        name={field.name}
                        defaultValue={field.value || ''}
                        validate={getValidatorOrFallback(
                          field.validateFunc,
                          field.requiredMessageText ? validationSchema().required(field.requiredMessageText) : null,
                        )}
                      >
                        {({ handleChange, handleBlur, handleFocus, touched, valid }) => {
                          const isError = touched[field.name] && !valid[field.name];
                          return (
                            <>
                              <PhoneExtensionInput
                                name={field.name}
                                labelText={field.title}
                                requiredText={field.requiredLabelText}
                                isTextInputError={false}
                                handleBlur={handleBlur}
                                handleFocus={handleFocus}
                                regularExp={getRegexFromString(field.validationExtension?.regex || '')}
                                controlKeys={CONTROL_KEYS}
                                value={field.value}
                                handleChange={(e) => handleChange(e.target.value)}
                                className="information-edit-form__field"
                                maxLength={field?.validationExtension?.maxCharacters}
                              />
                              <FormMessage error={isError} message={isError && field.requiredMessageText} />
                            </>
                          );
                        }}
                      </FormElement>
                    )}
                  </div>
                );
              }

              // Fallback for all other fields
              return (
                <div key={i} className="information-edit-form__row">
                  <GeTextInput
                    className="information-edit-form__field"
                    disabled={!!field.disabled}
                    labelText={field.title}
                    name={field.name}
                    defaultValue={field.value || ''}
                    placeHolder={field.placeholder}
                    required={!!field.requiredMessageText}
                    requiredText={field.requiredLabelText}
                    hintText={field.hintText}
                    validate={getValidatorOrFallback(
                      field.validateFunc,
                      field.requiredMessageText ? validationSchema().required(field.requiredMessageText) : null,
                    )}
                  />
                </div>
              );
            }
          }
        })}
        <FormConnector>
          {({ ResetForm, form: { status }, SetTouched }) => (
            <div className="information-edit-form__actions">
              <div className="information-edit-form__submit">
                <GeButton
                  type="submit"
                  className="information-edit-form__button"
                  disabled={!status.dirty || !status.valid || status.submitting || isPhoneNumberError}
                  btnStyleType="solid-primary"
                  btnSize="medium"
                  showLoadingSpinner={isSubmitting}
                  {...prepareDataAnalyticsAttributes(
                    analyticsAttributes?.length
                      ? [
                          ...analyticsAttributes,
                          { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                          { name: 'linkName', value: saveButtonText },
                        ]
                      : [
                          { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                          { name: 'linkName', value: saveButtonText },
                        ],
                  )}
                  btnLabel={saveButtonText}
                />
              </div>
              <GeButton
                className="information-edit-form__button"
                disabled={!status.dirty || status.submitting}
                btnStyleType="stroked-secondary"
                btnSize="medium"
                onClick={() => {
                  if (handleCancel) {
                    handleCancel();
                  }
                  ResetForm({ resetAnalytics: false, resetToDefaultValues: true });
                  SetTouched(FORM_FIELDS.FIRST_NAME);
                  SetTouched(FORM_FIELDS.LAST_NAME);
                }}
                {...prepareDataAnalyticsAttributes([
                  ...analyticsAttributes,
                  { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                  { name: 'linkName', value: cancelButtonText },
                ])}
                btnLabel={cancelButtonText}
              />
            </div>
          )}
        </FormConnector>
      </Form>
    </div>
  );
};
