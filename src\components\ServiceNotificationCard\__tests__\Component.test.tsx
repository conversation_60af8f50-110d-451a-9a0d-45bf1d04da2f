import React from 'react';
import { render, screen } from '@testing-library/react';
import { ServiceNotificationCard } from '../Component';
import { NotificationCardProps } from '../models';

// Mock the Text component from cx-dle-component-library
jest.mock('cx-dle-component-library', () => ({
  Text: ({ text, className, tag: Tag = 'div' }: { text: string; className?: string; tag?: string }) =>
    React.createElement(Tag, { className, 'data-testid': 'text-component' }, text),
}));

describe('ServiceNotificationCard', () => {
  const mockPreferencesComponent = <div data-testid="preferences">Mock Preferences</div>;

  const defaultProps: NotificationCardProps = {
    infoTitle: 'Test Info Title',
    descriptionLabel: 'Test Description Label',
    showDescriptionLabel: true,
    preferencesComponent: mockPreferencesComponent,
    editLinkText: 'Edit',
    closeLinkText: 'Close',
  };

  it('renders info title correctly', () => {
    render(<ServiceNotificationCard {...defaultProps} />);
    expect(screen.getByText('Test Info Title')).toBeInTheDocument();
  });

  it('renders description label when showDescriptionLabel is true', () => {
    render(<ServiceNotificationCard {...defaultProps} />);
    expect(screen.getByText('Test Description Label')).toBeInTheDocument();
    expect(screen.queryByTestId('preferences')).not.toBeInTheDocument();
  });

  it('renders preferences component when showDescriptionLabel is false', () => {
    const props = {
      ...defaultProps,
      showDescriptionLabel: false,
    };
    render(<ServiceNotificationCard {...props} />);
    expect(screen.queryByText('Test Description Label')).not.toBeInTheDocument();
    expect(screen.getByTestId('preferences')).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    const { container } = render(<ServiceNotificationCard {...defaultProps} />);
    expect(container.querySelector('.ge-user-preferences__info')).toBeInTheDocument();
    expect(container.querySelector('.ge-user-preferences__info-title')).toBeInTheDocument();
    expect(container.querySelector('.ge-user-preferences__info-names')).toBeInTheDocument();
  });
});
