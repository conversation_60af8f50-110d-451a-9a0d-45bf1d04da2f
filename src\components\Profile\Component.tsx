import { useQuery } from '@apollo/client';
import PersonalInformationComponent from '../../components/PersonalInformation';
import ProfessionalInformation from '../../components/ProfessionalInformation';
import { usePageData, UserInfo, userProfessionalInfoQuery, useUserInfo } from '../../hooks';
import { ProfileTab as ProfileTabProps } from '../../models/ProfileTab';
import { getLocaleFromHostname } from '../../utils';

export const ProfileTab = () => {
  const { data: tabData, isLoading: isProfileLoading } = usePageData<ProfileTabProps>(
    `settings/profileTab`,
    'profileData',
  );

  const { data: userInfo, loading, refetch, error } = useUserInfo();
  const {
    data: userLocalNamesData,
    loading: isLoadingUserLocalNamesData,
    error: isErrorUserLocalNamesData,
    refetch: refetchUserLocalNames,
  } = useQuery(userProfessionalInfoQuery, { skip: getLocaleFromHostname() == 'ja-jp' ? false : true });

  if (isProfileLoading) {
    return null;
  }

  return (
    <>
      {!loading && (
        <>
          <PersonalInformationComponent
            {...tabData?.personalInfo}
            userInfo={userInfo as UserInfo}
            userLocalNames={
              userLocalNamesData?.userProfessionalInfo?.contactDetail ?? { localFirstName: '', localLastName: '' }
            }
            loading={loading || isLoadingUserLocalNamesData}
            refetch={() => {
              refetch({ fetchPolicy: 'no-cache' });
              getLocaleFromHostname() == 'ja-jp' && refetchUserLocalNames({ fetchPolicy: 'no-cache' });
            }}
            error={error || isErrorUserLocalNamesData}
          />
          <ProfessionalInformation
            {...tabData?.professionalInfo}
            userInfo={userInfo as UserInfo}
            loading={loading}
            refetch={() => {
              refetch({ fetchPolicy: 'no-cache' });
            }}
            error={error}
          />
        </>
      )}
    </>
  );
};
