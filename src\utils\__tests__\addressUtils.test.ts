import { formatEquipmentLocationForTooltipV2 } from '../addressUtils';

describe('addressUtils', () => {
  describe('formatEquipmentLocationForTooltipV2', () => {
    it('returns empty string when physicalLocationAddress is null', () => {
      const result = formatEquipmentLocationForTooltipV2(null);
      expect(result).toBe('');
    });

    it('returns empty string when physicalLocationAddress is undefined', () => {
      const result = formatEquipmentLocationForTooltipV2(undefined);
      expect(result).toBe('');
    });

    it('formats address with only location name', () => {
      const address = {
        physicalLocationName: 'Main Hospital',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('Main Hospital');
    });

    it('formats address with location name and city', () => {
      const address = {
        physicalLocationName: 'Main Hospital',
        physicalLocationCity: 'New York',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('Main Hospital\nNew York,');
    });

    it('formats address with location name, city, and region', () => {
      const address = {
        physicalLocationName: 'Main Hospital',
        physicalLocationCity: 'New York',
        physicalLocationRegion: 'NY',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('Main Hospital\nNew York,\nNY,');
    });

    it('formats complete address with all fields', () => {
      const address = {
        physicalLocationName: 'Main Hospital',
        physicalLocationCity: 'New York',
        physicalLocationRegion: 'NY',
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('Main Hospital\nNew York,\nNY,\nUSA');
    });

    it('formats address without location name but with other fields', () => {
      const address = {
        physicalLocationCity: 'New York',
        physicalLocationRegion: 'NY',
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('New York,\nNY,\nUSA');
    });

    it('formats address with only city', () => {
      const address = {
        physicalLocationCity: 'New York',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('New York,');
    });

    it('formats address with only region', () => {
      const address = {
        physicalLocationRegion: 'NY',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('NY,');
    });

    it('formats address with only country', () => {
      const address = {
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('USA');
    });

    it('handles empty string values', () => {
      const address = {
        physicalLocationName: '',
        physicalLocationCity: 'New York',
        physicalLocationRegion: '',
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('New York,\nUSA');
    });

    it('handles mixed empty and valid values', () => {
      const address = {
        physicalLocationName: 'Main Hospital',
        physicalLocationCity: '',
        physicalLocationRegion: 'NY',
        physicalLocationCountry: '',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('Main Hospital\nNY,');
    });

    it('removes leading newlines correctly', () => {
      const address = {
        physicalLocationCity: 'New York',
        physicalLocationRegion: 'NY',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      // Should not start with newline
      expect(result).not.toMatch(/^\n/);
      expect(result).toBe('New York,\nNY,');
    });

    it('handles address with special characters', () => {
      const address = {
        physicalLocationName: 'St. Mary\'s Hospital & Medical Center',
        physicalLocationCity: 'São Paulo',
        physicalLocationRegion: 'SP',
        physicalLocationCountry: 'Brasil',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('St. Mary\'s Hospital & Medical Center\nSão Paulo,\nSP,\nBrasil');
    });

    it('handles address with numeric values', () => {
      const address = {
        physicalLocationName: 'Hospital 123',
        physicalLocationCity: 'City 456',
        physicalLocationRegion: 'Region 789',
        physicalLocationCountry: 'Country 000',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('Hospital 123\nCity 456,\nRegion 789,\nCountry 000');
    });

    it('handles very long address values', () => {
      const address = {
        physicalLocationName: 'Very Long Hospital Name That Exceeds Normal Length Expectations',
        physicalLocationCity: 'Very Long City Name That Also Exceeds Normal Length',
        physicalLocationRegion: 'Very Long Region Name',
        physicalLocationCountry: 'Very Long Country Name',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toContain('Very Long Hospital Name That Exceeds Normal Length Expectations');
      expect(result).toContain('Very Long City Name That Also Exceeds Normal Length');
      expect(result).toContain('Very Long Region Name');
      expect(result).toContain('Very Long Country Name');
    });

    it('handles address with whitespace-only values', () => {
      const address = {
        physicalLocationName: '   ',
        physicalLocationCity: 'New York',
        physicalLocationRegion: '   ',
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('   \nNew York,\n   ,\nUSA');
    });

    it('handles completely empty address object', () => {
      const address = {};
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('');
    });

    it('handles address with null values', () => {
      const address = {
        physicalLocationName: null,
        physicalLocationCity: 'New York',
        physicalLocationRegion: null,
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('New York,\nUSA');
    });

    it('handles address with undefined values', () => {
      const address = {
        physicalLocationName: undefined,
        physicalLocationCity: 'New York',
        physicalLocationRegion: undefined,
        physicalLocationCountry: 'USA',
      };
      const result = formatEquipmentLocationForTooltipV2(address);
      expect(result).toBe('New York,\nUSA');
    });
  });
});
