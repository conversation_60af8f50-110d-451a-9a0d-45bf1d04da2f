import { ProfileInfoType } from "../../../models/Account"

export interface CardInfoProps {
  showSkeletonLoader: boolean
  profileInfo: any
  organization: any
  cardType: keyof ProfileCardIconsType
  showCommunicationPreferences?: boolean
  props: ProfileInfoType
  hasNotification?: boolean
  notificationCount?: string
  showNotificationCard?: boolean
}

export type ProfileCardIconsType = {
  profile: string;
  help: string;
  notification: string;
};