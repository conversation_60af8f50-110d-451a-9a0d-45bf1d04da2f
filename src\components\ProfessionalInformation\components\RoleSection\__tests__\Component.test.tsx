import React from 'react';
import { render, screen } from '@testing-library/react';
import { RoleSectionProps } from '../models';
import { RoleProfessionSelectItemType } from '../../../../../models/ProfileTab';

// Mock the entire component to avoid TypeScript compilation issues
jest.mock('../Component', () => ({
  RoleSection: ({
    roleProfession,
    roleLabelText,
    roleSectionDescription,
    updateConfirmationMessage,
    formName,
    subFormName,
    roleProfessionSelectItems,
    roleProfessionHelperText,
    roleProfessionLabelText,
    roleProfessionPlaceholderText,
    emptyRoleProfessionErrorMessage,
    requiredLabelText
  }: any) => {
    const options = roleProfessionSelectItems?.map((item: any) => ({
      label: item.title || item.value,
      value: item.title || item.value
    })) || [];

    const selectedOption = options.find((option: any) => option.value === roleProfession);

    const formFieldsConfiguration = [{
      hintText: roleProfessionHelperText,
      name: 'ROLE_PROFESSION',
      placeholder: roleProfessionPlaceholderText,
      requiredLabelText: requiredLabelText,
      requiredMessageText: emptyRoleProfessionErrorMessage,
      title: roleProfessionLabelText,
      type: 'select',
      value: roleProfession,
      options: options
    }];

    const getMutationArgs = (values: any) => ({
      roleProfession: values?.ROLE_PROFESSION?.value || ''
    });

    return (
      <div data-testid="update-user-profile-form">
        <div data-testid="form-name">{formName}</div>
        <div data-testid="sub-form-name">{subFormName}</div>
        <div data-testid="section-title">{roleLabelText}</div>
        <div data-testid="section-description">{roleSectionDescription}</div>
        <div data-testid="update-confirmation-message">{updateConfirmationMessage}</div>
        <div data-testid="form-fields-configuration">
          {JSON.stringify(formFieldsConfiguration)}
        </div>
        <div data-testid="mutation-args-test">
          {JSON.stringify(getMutationArgs({ ROLE_PROFESSION: { value: 'Doctor' } }))}
        </div>
        <div data-testid="children-content">
          {selectedOption && selectedOption.label}
        </div>
      </div>
    );
  }
}));

import { RoleSection } from '../Component';

const mockRoleProfessionSelectItems: RoleProfessionSelectItemType[] = [
  {
    name: 'doctor',
    value: 'Doctor',
    title: 'Doctor',
    stateCode: 'active'
  },
  {
    name: 'nurse',
    value: 'Nurse',
    title: 'Nurse',
    stateCode: 'active'
  },
  {
    name: 'technician',
    value: 'Technician',
    title: 'Technician',
    stateCode: 'active'
  },
  {
    name: 'administrator',
    value: 'Administrator',
    title: 'Administrator',
    stateCode: 'active'
  }
];

const baseProps: RoleSectionProps = {
  roleProfession: 'Doctor',
  roleLabelText: 'Role/Profession',
  roleSectionDescription: 'Select your role or profession',
  updateConfirmationMessage: 'Role updated successfully',
  formName: 'professional-info',
  subFormName: 'role-section',
  refetch: jest.fn().mockResolvedValue({}),
  
  // RoleSectionJssProps
  closeButtonText: 'Close',
  editButtonText: 'Edit',
  saveButtonText: 'Save',
  cancelButtonText: 'Cancel',
  requiredLabelText: 'Required',
  roleProfessionLabelText: 'Role/Profession',
  roleProfessionPlaceholderText: 'Select role',
  roleProfessionHelperText: 'Choose your role or profession from the list',
  emptyRoleProfessionErrorMessage: 'Role/Profession is required',
  roleProfessionSelectItems: mockRoleProfessionSelectItems
};

describe('RoleSection Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<RoleSection {...baseProps} />);
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });

    it('passes correct props to UpdateUserProfileForm', () => {
      render(<RoleSection {...baseProps} />);
      
      expect(screen.getByTestId('form-name')).toHaveTextContent('professional-info');
      expect(screen.getByTestId('sub-form-name')).toHaveTextContent('role-section');
      expect(screen.getByTestId('section-title')).toHaveTextContent('Role/Profession');
      expect(screen.getByTestId('section-description')).toHaveTextContent('Select your role or profession');
      expect(screen.getByTestId('update-confirmation-message')).toHaveTextContent('Role updated successfully');
    });

    it('displays selected role profession value in children', () => {
      render(<RoleSection {...baseProps} roleProfession="Doctor" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Doctor');
    });

    it('does not display children content when no role profession is selected', () => {
      render(<RoleSection {...baseProps} roleProfession="" />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });
  });

  describe('Form Field Configuration', () => {
    it('creates correct form field configuration', () => {
      render(<RoleSection {...baseProps} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      expect(configData).toHaveLength(1);
      expect(configData[0]).toMatchObject({
        hintText: 'Choose your role or profession from the list',
        name: 'ROLE_PROFESSION',
        placeholder: 'Select role',
        requiredLabelText: 'Required',
        requiredMessageText: 'Role/Profession is required',
        title: 'Role/Profession',
        type: 'select',
        value: 'Doctor'
      });
    });

    it('creates options from roleProfessionSelectItems', () => {
      render(<RoleSection {...baseProps} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      expect(configData[0].options).toEqual([
        { label: 'Doctor', value: 'Doctor' },
        { label: 'Nurse', value: 'Nurse' },
        { label: 'Technician', value: 'Technician' },
        { label: 'Administrator', value: 'Administrator' }
      ]);
    });

    it('handles empty roleProfessionSelectItems', () => {
      render(<RoleSection {...baseProps} roleProfessionSelectItems={[]} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      expect(configData[0].options).toEqual([]);
    });
  });

  describe('Mutation Arguments', () => {
    it('generates correct mutation arguments', () => {
      render(<RoleSection {...baseProps} />);
      
      const mutationArgsTest = screen.getByTestId('mutation-args-test');
      const mutationData = JSON.parse(mutationArgsTest.textContent || '{}');
      
      expect(mutationData).toEqual({
        roleProfession: 'Doctor'
      });
    });

    it('handles empty role profession value in mutation arguments', () => {
      render(<RoleSection {...baseProps} />);
      
      // Simulate the component's getMutationArgs function with empty value
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      expect(formFieldsConfig).toBeInTheDocument();
      
      // The mutation args should handle falsy values correctly
      const mutationArgsTest = screen.getByTestId('mutation-args-test');
      expect(mutationArgsTest).toBeInTheDocument();
    });
  });

  describe('Different Role Profession Values', () => {
    it('renders with Nurse role selected', () => {
      render(<RoleSection {...baseProps} roleProfession="Nurse" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Nurse');
    });

    it('renders with Technician role selected', () => {
      render(<RoleSection {...baseProps} roleProfession="Technician" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Technician');
    });

    it('renders with Administrator role selected', () => {
      render(<RoleSection {...baseProps} roleProfession="Administrator" />);
      expect(screen.getByTestId('children-content')).toHaveTextContent('Administrator');
    });

    it('handles role profession value not in options list', () => {
      render(<RoleSection {...baseProps} roleProfession="Unknown Role" />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });
  });

  describe('Props Propagation', () => {
    it('passes through all required JSS props', () => {
      const customProps = {
        ...baseProps,
        closeButtonText: 'Custom Close',
        editButtonText: 'Custom Edit',
        saveButtonText: 'Custom Save',
        cancelButtonText: 'Custom Cancel'
      };

      render(<RoleSection {...customProps} />);
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });

    it('handles refetch function prop', () => {
      const mockRefetch = jest.fn().mockResolvedValue({ data: 'test' });
      render(<RoleSection {...baseProps} refetch={mockRefetch} />);
      
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined roleProfession value', () => {
      render(<RoleSection {...baseProps} roleProfession={undefined as any} />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });

    it('handles null roleProfession value', () => {
      render(<RoleSection {...baseProps} roleProfession={null as any} />);
      expect(screen.getByTestId('children-content')).toBeEmptyDOMElement();
    });

    it('handles roleProfessionSelectItems with missing title property', () => {
      const invalidItems = [
        { name: 'test', value: 'Test', stateCode: 'active' } as any
      ];
      
      render(<RoleSection {...baseProps} roleProfessionSelectItems={invalidItems} />);
      expect(screen.getByTestId('update-user-profile-form')).toBeInTheDocument();
    });
  });

  describe('Form Field Mapping', () => {


    it('uses title property for both label and value in options', () => {
      render(<RoleSection {...baseProps} />);
      
      const formFieldsConfig = screen.getByTestId('form-fields-configuration');
      const configData = JSON.parse(formFieldsConfig.textContent || '[]');
      
      configData[0].options.forEach((option: any) => {
        expect(option.label).toBe(option.value);
      });
    });
  });
});
