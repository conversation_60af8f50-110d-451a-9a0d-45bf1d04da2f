import './styles.scss';
import { useEffect, useState } from 'react';
import { Ge<PERSON>utton, GridContainer, GridRow, GridCell, EDSIcon, LoadableSection, Snackbar, ErrorMessage} from 'cx-dle-component-library';
import { notificationMutation, NotificationsListQuery } from './query';
import { useLocaleMoment, usePagination } from 'cx-dle-common-lib';
import { useCommonPageData, usePageData } from '../../hooks/common/usePageData';
import { EDSLink } from 'cx-dle-component-library/components/EDS/components/EDSLink';
import { useMutation } from '@apollo/client';
import { TQueryVariables } from 'cx-dle-common-lib/Hooks/usePagination/models';
import { ActivityTimelineType, Notifications, NotificationsType, } from '../../models/Notifications';
import { NotificationsListQuery as NotificationsListQueryType, QueryContextWithSort, SortBy } from './models';
import { LinkField } from 'cx-dle-component-library/components/EDS/RedesignFoundation/Link/models';
import { toServiceNotificationsList } from '../../components/NotificationsList/withMappers/handlers';
import PageTitle from '../../components/Common/PageTitle';
import NotificationsGroupedList from '../../components/NotificationsList/components/NotificationsGroupedList';
import { Sitecommoncontent } from '../../models/Sitecommoncontent';
import { Dlesitecommoncontent } from '../../models/Dlesitecommoncontent';
import LoadMore from '../../components/Common/LoadMore';
import ActivityTimelineWrapper from '../../components/ActivityTimeline';
import { useLanguage } from '../../context';
import { Metadata } from '../../components/Metadata';

import { useTranslation } from 'react-i18next';
import { DocumentProvider } from '../../context/DocumentProviderContext';
import BreadcrumbControl from '../../components/BreadcrumbNavigationNew/Component';

export const NotificationsList = () => {
  let setReadNotification = false;
  let showNoMessage = false;

  const [notification, setNotification] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const [isShowTimeline, setIsShowTimline] = useState(false);
  const [serviceEventId, setServiceEventId] = useState("");
  const [error, setError] = useState(false);

  const { data: pageData, isLoading: isPageDataLoading, isError } = usePageData<Notifications>('notifications', 'notificationsData');
  const { data: sfdcNotificationData, isLoading: isSfdcNotificationDataLoading } = usePageData<Sitecommoncontent>('sitecommoncontent', 'sitecommoncontentData');
  const { data: modalityData, isLoading: isModalityDataLoading } = useCommonPageData<Dlesitecommoncontent>('dlesitecommoncontent', 'dlesitecommoncontentData');
  const {language} = useLanguage();
  const dateTimeUtils=  useLocaleMoment(language);
  const {t} = useTranslation();


  const { data, loadMore, loading, isFetchingNextPage, refetch, error: apiError} = usePagination<NotificationsListQueryType.Query, TQueryVariables>(NotificationsListQuery, {
    variables: {
      queryContext: {
        pageOffset: {
          cursorMark: '',
          rows: parseInt(pageData?.notifications?.numberOfCardsToRequestFirstTime ?? '10', 10) || 10
        },
        sort: { creationTimestamp: SortBy.Desc },
      } as QueryContextWithSort,
    },
    notifyOnNetworkStatusChange: true,
  });

  const [updateReadNotifications] = useMutation(notificationMutation);

  const { items, totalRows } = data?.collection || {} as NotificationsListQueryType.Collection;

  const updateReadFlag = async () => {
    await updateReadNotifications({
      variables: {
        input: { read: true },
      },
    });
  };

  const activityConfig= {
    isNewUXEquipementCard: true,
    isServiceEventV2QueryServiceTracker: true,
    enableFullAddressForLocation: true,
    inProgressWorkOrdersSortAsc: true
  }
  
  const timelineLocalizationData= {
    serviceStateMessageCodesLocalization: pageData?.notifications.serviceStateMessageCodesLocalization,
    engineerTypeCodesLocalization: pageData?.notifications.engineerTypeCodesLocalization,
    srStatusLocalization: modalityData?.srStatusLocalizations,
    modalityLocalization: modalityData?.modalityLocalizations,
    serviceEventTypeLocalization: modalityData?.serviceEventTypeLocalizations
  } 

  useEffect(() => {
    if (!setReadNotification && totalRows > 0 && !hasTriggered) {
      setReadNotification = true;
      updateReadFlag();
      setNotification(false);
      setHasTriggered(true);
    }
  }, [totalRows, hasTriggered]);

  useEffect(()=>{
    if(apiError){
      setError(true)
    }
  },[apiError])

  const handleCloseButton = ()=>{
    setIsShowTimline(false);
  }

  return (
    <>
      {pageData?.metadata && <Metadata metadata={pageData.metadata} />}
      <LoadableSection loading={isPageDataLoading || isModalityDataLoading || isSfdcNotificationDataLoading} error={isError}>{() =>{
        const mappedData = toServiceNotificationsList(items || [], pageData?.notifications as NotificationsType, modalityData as Dlesitecommoncontent, sfdcNotificationData as Sitecommoncontent);
       return <>
        {error && <Snackbar
                showCloseButton={true}
                type="danger"
                onCloseClick={() => {
                  setError(false);
                }}
               >
                <ErrorMessage message={t("Errors.Default")} />
              </Snackbar>}

         {isShowTimeline && <DocumentProvider>
           <ActivityTimelineWrapper
             handleCloseButton={handleCloseButton}
             seviceEventId={serviceEventId || ""}
             componentInternalError={modalityData?.dictionaryItems?.componentInternalError || ""}
             timelineLocalizationData={timelineLocalizationData}
             dateTimeUtils={dateTimeUtils} activityConfig={activityConfig}
             activityTimelineStaticData={pageData?.activityTimeline as ActivityTimelineType}
           />
         </DocumentProvider>}
         <BreadcrumbControl
          breadcrumbLink={pageData?.notifications.breadcrumb.breadcrumbLink.href ?? ''}
          breadcrumbNavigationTitle={pageData?.notifications.breadcrumb.breadcrumbLink.text ?? ''}
          breadcrumbPageTitle={pageData?.notifications.breadcrumb.breadcrumbPageTitle ?? ''}>
         </BreadcrumbControl> 
          <GridContainer>
            <GridRow className='ge-pageTitle' >
              <GridCell desktop={12} tablet={12} phone={12}>

                <PageTitle title={pageData?.pageTitle || ""} position="left" />

                <div className="ge-notifications-list__refresh">
                  {isFetchingNextPage && notification && (
                    <GeButton
                      btnIcon={['far', 'sync']}
                      btnLabel={pageData?.notifications.notificationRefreshButtonText}
                      disabled={false}
                      className="ge-notifications-list__refresh-button"
                      btnSize="small"
                      btnStyleType="solid-primary"
                      onClick={() => {
                        setReadNotification = false;
                        refetch();
                      }}
                      iconLeftAligned
                    />
                  )}
                </div>
              </GridCell>
            </GridRow>

            <div className={'ge-notifications-list ge-notifications-listV2'}>
              <div className={'ge-notifications-list__preferences ge-notifications-list__preferencesV2'}>
                <EDSLink
                  field={pageData?.notifications?.changePreferencesLink as unknown as LinkField}
                  showLinkTextWithChildrenPresent
                >
                  <EDSIcon icon={"ico-settings-16"}></EDSIcon>
                </EDSLink>

              </div>

              <GridRow className='ge-notifications-main' >
                <GridCell desktop={12} tablet={12} phone={12}>
                  <NotificationsGroupedList
                    loading={loading}
                    items={mappedData}
                    buttonText={pageData?.notifications.detailsButtonText || ""}
                    warrantyExpiredText={pageData?.notifications.notificationWarrantyExpiredText || ""}
                    contractExpiredText={pageData?.notifications.notificationContractExpiredText || ""}
                    overduePMText={pageData?.notifications.overduePMMessageText || ""}
                    groupingRules={pageData?.notifications.groupingRules || []}
                    onWatchMessageText={pageData?.notifications.onWatchMessageText || ""}
                    onHoldMessageText={pageData?.notifications.onHoldMessageText || ""}
                    dateFormat={pageData?.notifications?.dateFormat || ""}
                    selectRequestAsset={(serviceEventId) => { setServiceEventId(serviceEventId); setIsShowTimline(true); }}
                    noNotificationMessage={pageData?.notifications.noNotificationsMessageText || ""}
                    showNoMessage={(toggleValue: boolean) => showNoMessage = toggleValue}
                  />
                  <LoadMore
                    hasLoadMore={totalRows > 0 && mappedData.length < totalRows && !showNoMessage}
                    loading={!!isFetchingNextPage}
                    loadMoreButtonText={pageData?.notifications?.loadMoreButtonText || ""}
                    onLoadMoreButtonClick={()=>loadMore()}
                  />
                </GridCell>
              </GridRow>
            </div>

          </GridContainer>
        </>}}
      </LoadableSection>
    </>
  );
};
