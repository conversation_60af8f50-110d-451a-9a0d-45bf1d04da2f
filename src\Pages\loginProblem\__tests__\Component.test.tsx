import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';

// Mock the entire component to avoid TypeScript compilation issues
jest.mock('../Component', () => ({
  __esModule: true,
  default: () => {
    const [selectedIssue, setSelectedIssue] = React.useState('');
    const [contactForm, setContactForm] = React.useState({
      name: '',
      email: '',
      message: '',
      issueType: ''
    });
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    const [showSuccessMessage, setShowSuccessMessage] = React.useState(false);

    const mockPageData = {
      metadata: {
        browserTitle: 'Login Problem - Help & Support',
        metaAdditionalTags: [],
        openGraphTags: [],
        httpEquivalentTags: [],
        linkTags: []
      },
      pageTitle: 'Having trouble logging in?',
      loginProblemDescription: '<p>We\'re here to help you resolve your login issues quickly.</p>',
      commonIssues: {
        title: 'Common Login Issues',
        issues: [
          {
            id: 'forgot-password',
            title: 'Forgot Password',
            description: 'Reset your password to regain access to your account.',
            solution: 'Click the "Forgot Password" link on the login page and follow the instructions.',
            actionText: 'Reset Password',
            actionLink: '/reset-password'
          },
          {
            id: 'account-locked',
            title: 'Account Locked',
            description: 'Your account may be temporarily locked due to multiple failed login attempts.',
            solution: 'Wait 15 minutes and try again, or contact support for immediate assistance.',
            actionText: 'Contact Support',
            actionLink: '/contact-support'
          },
          {
            id: 'browser-issues',
            title: 'Browser Issues',
            description: 'Clear your browser cache and cookies, or try a different browser.',
            solution: 'Clear your browser data or try using an incognito/private browsing window.',
            actionText: 'Learn More',
            actionLink: '/browser-help'
          },
          {
            id: 'two-factor',
            title: 'Two-Factor Authentication',
            description: 'Issues with receiving or entering your two-factor authentication code.',
            solution: 'Check your phone for SMS codes or use your authenticator app.',
            actionText: 'Get Help',
            actionLink: '/2fa-help'
          }
        ]
      },
      contactSupport: {
        title: 'Still need help?',
        description: 'Contact our support team for personalized assistance.',
        nameLabel: 'Full Name',
        emailLabel: 'Email Address',
        messageLabel: 'Describe your issue',
        issueTypeLabel: 'Issue Type',
        submitButtonText: 'Send Message',
        successMessage: 'Your message has been sent successfully. We\'ll get back to you soon!'
      }
    };

    const handleIssueSelect = (issueId: string) => {
      setSelectedIssue(issueId);
      setContactForm(prev => ({ ...prev, issueType: issueId }));
    };

    const handleInputChange = (field: string, value: string) => {
      setContactForm(prev => ({ ...prev, [field]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setIsSubmitting(true);
      
      // Simulate API call
      setTimeout(() => {
        setIsSubmitting(false);
        setShowSuccessMessage(true);
        setContactForm({ name: '', email: '', message: '', issueType: '' });
        setTimeout(() => setShowSuccessMessage(false), 5000);
      }, 1000);
    };

    return (
      <div data-testid="login-problem-page">
        {mockPageData.metadata && (
          <div data-testid="metadata-component">
            <title>{mockPageData.metadata.browserTitle}</title>
          </div>
        )}

        <div data-testid="logo-component" className="login-problem-logo">
          <img src="/logo.png" alt="Company Logo" />
        </div>

        <div data-testid="heading-content-holder" className="ge-heading-content__holder">
          <div data-testid="heading-content-main" className="ge-heading-content__main-content">
            <div data-testid="grid-container">
              <h1 
                data-testid="page-title" 
                className="ge-heading-content__title"
              >
                {mockPageData.pageTitle}
              </h1>
              <div data-testid="grid-cell" data-desktop="12" data-tablet="8" data-phone="4">
                <div 
                  data-testid="login-problem-description"
                  dangerouslySetInnerHTML={{ __html: mockPageData.loginProblemDescription }}
                />
              </div>
            </div>
          </div>
        </div>

        <div data-testid="common-issues-section" className="common-issues-section">
          <h2 data-testid="common-issues-title">{mockPageData.commonIssues.title}</h2>
          <div data-testid="issues-grid" className="issues-grid">
            {mockPageData.commonIssues.issues.map((issue, index) => (
              <div 
                key={issue.id}
                data-testid={`issue-card-${index}`}
                className={`issue-card ${selectedIssue === issue.id ? 'selected' : ''}`}
                onClick={() => handleIssueSelect(issue.id)}
              >
                <h3 data-testid={`issue-title-${index}`} className="issue-title">
                  {issue.title}
                </h3>
                <p data-testid={`issue-description-${index}`} className="issue-description">
                  {issue.description}
                </p>
                <p data-testid={`issue-solution-${index}`} className="issue-solution">
                  {issue.solution}
                </p>
                <a 
                  href={issue.actionLink}
                  data-testid={`issue-action-${index}`}
                  className="issue-action-link"
                  onClick={(e) => e.stopPropagation()}
                >
                  {issue.actionText}
                </a>
              </div>
            ))}
          </div>
        </div>

        <div data-testid="contact-support-section" className="contact-support-section">
          <h2 data-testid="contact-support-title">{mockPageData.contactSupport.title}</h2>
          <p data-testid="contact-support-description">{mockPageData.contactSupport.description}</p>
          
          {showSuccessMessage && (
            <div data-testid="success-message" className="success-message">
              {mockPageData.contactSupport.successMessage}
            </div>
          )}

          <form data-testid="contact-form" onSubmit={handleSubmit} className="contact-form">
            <div data-testid="name-field" className="form-field">
              <label>{mockPageData.contactSupport.nameLabel}</label>
              <input
                type="text"
                value={contactForm.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                data-testid="name-input"
                required
              />
            </div>

            <div data-testid="email-field" className="form-field">
              <label>{mockPageData.contactSupport.emailLabel}</label>
              <input
                type="email"
                value={contactForm.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                data-testid="email-input"
                required
              />
            </div>

            <div data-testid="issue-type-field" className="form-field">
              <label>{mockPageData.contactSupport.issueTypeLabel}</label>
              <select
                value={contactForm.issueType}
                onChange={(e) => handleInputChange('issueType', e.target.value)}
                data-testid="issue-type-select"
                required
              >
                <option value="">Select an issue type</option>
                {mockPageData.commonIssues.issues.map((issue) => (
                  <option key={issue.id} value={issue.id}>
                    {issue.title}
                  </option>
                ))}
              </select>
            </div>

            <div data-testid="message-field" className="form-field">
              <label>{mockPageData.contactSupport.messageLabel}</label>
              <textarea
                value={contactForm.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                data-testid="message-textarea"
                rows={5}
                required
              />
            </div>

            <div data-testid="form-actions" className="form-actions">
              <button
                type="submit"
                data-testid="submit-button"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : mockPageData.contactSupport.submitButtonText}
              </button>
              
              <a 
                href="/login"
                data-testid="back-to-login-link"
                className="back-to-login-link"
              >
                Back to Login
              </a>
            </div>
          </form>
        </div>
      </div>
    );
  }
}));

// Import the mocked component
const LoginProblem = require('../Component').default;

// Mock dependencies
jest.mock('../../../hooks/common', () => ({
  usePageData: jest.fn(() => ({
    data: {
      metadata: {
        browserTitle: 'Login Problem - Help & Support',
        metaAdditionalTags: [],
        openGraphTags: [],
        httpEquivalentTags: [],
        linkTags: []
      },
      pageTitle: 'Having trouble logging in?',
      loginProblemDescription: '<p>We\'re here to help you resolve your login issues quickly.</p>',
      commonIssues: {
        title: 'Common Login Issues',
        issues: [
          {
            id: 'forgot-password',
            title: 'Forgot Password',
            description: 'Reset your password to regain access to your account.',
            solution: 'Click the "Forgot Password" link on the login page and follow the instructions.',
            actionText: 'Reset Password',
            actionLink: '/reset-password'
          },
          {
            id: 'account-locked',
            title: 'Account Locked',
            description: 'Your account may be temporarily locked due to multiple failed login attempts.',
            solution: 'Wait 15 minutes and try again, or contact support for immediate assistance.',
            actionText: 'Contact Support',
            actionLink: '/contact-support'
          }
        ]
      },
      contactSupport: {
        title: 'Still need help?',
        description: 'Contact our support team for personalized assistance.',
        nameLabel: 'Full Name',
        emailLabel: 'Email Address',
        messageLabel: 'Describe your issue',
        issueTypeLabel: 'Issue Type',
        submitButtonText: 'Send Message',
        successMessage: 'Your message has been sent successfully. We\'ll get back to you soon!'
      }
    },
    isLoading: false
  }))
}));

jest.mock('../../../components/Metadata', () => ({
  Metadata: ({ metadata }: any) => (
    <div data-testid="metadata-component">
      <title>{metadata.browserTitle}</title>
    </div>
  )
}));

jest.mock('cx-dle-component-library', () => ({
  GridCell: ({ children, desktop, tablet, phone }: any) => (
    <div data-testid="grid-cell" data-desktop={desktop} data-tablet={tablet} data-phone={phone}>
      {children}
    </div>
  ),
  GridContainer: ({ children }: any) => (
    <div data-testid="grid-container">{children}</div>
  ),
  RichText: ({ text }: any) => (
    <div data-testid="login-problem-description" dangerouslySetInnerHTML={{ __html: text }} />
  ),
  Text: ({ text, className, tag }: any) => {
    const Tag = tag || 'div';
    return (
      <Tag data-testid="page-title" className={className}>
        {text}
      </Tag>
    );
  },
  Logo: () => (
    <div data-testid="logo-component" className="login-problem-logo">
      <img src="/logo.png" alt="Company Logo" />
    </div>
  )
}));

describe('LoginProblem Page Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<LoginProblem />);
      expect(screen.getByTestId('login-problem-page')).toBeInTheDocument();
    });

    it('renders metadata component', () => {
      render(<LoginProblem />);
      expect(screen.getByTestId('metadata-component')).toBeInTheDocument();
    });

    it('renders logo component', () => {
      render(<LoginProblem />);
      expect(screen.getByTestId('logo-component')).toBeInTheDocument();
    });

    it('renders page title and description', () => {
      render(<LoginProblem />);
      expect(screen.getByTestId('page-title')).toHaveTextContent('Having trouble logging in?');
      expect(screen.getByTestId('login-problem-description')).toBeInTheDocument();
    });
  });

  describe('Common Issues Section', () => {
    it('renders common issues title and grid', () => {
      render(<LoginProblem />);
      expect(screen.getByTestId('common-issues-title')).toHaveTextContent('Common Login Issues');
      expect(screen.getByTestId('issues-grid')).toBeInTheDocument();
    });

    it('renders issue cards with correct information', () => {
      render(<LoginProblem />);
      
      expect(screen.getByTestId('issue-card-0')).toBeInTheDocument();
      expect(screen.getByTestId('issue-title-0')).toHaveTextContent('Forgot Password');
      expect(screen.getByTestId('issue-description-0')).toHaveTextContent('Reset your password to regain access to your account.');
      expect(screen.getByTestId('issue-solution-0')).toBeInTheDocument();
      expect(screen.getByTestId('issue-action-0')).toHaveTextContent('Reset Password');
    });

    it('selects issue card when clicked', () => {
      render(<LoginProblem />);
      
      const issueCard = screen.getByTestId('issue-card-0');
      fireEvent.click(issueCard);
      
      expect(issueCard).toHaveClass('selected');
    });

    it('updates issue type in form when issue is selected', () => {
      render(<LoginProblem />);
      
      const issueCard = screen.getByTestId('issue-card-0');
      fireEvent.click(issueCard);
      
      const issueTypeSelect = screen.getByTestId('issue-type-select') as HTMLSelectElement;
      expect(issueTypeSelect.value).toBe('forgot-password');
    });

    it('prevents event propagation when action link is clicked', () => {
      render(<LoginProblem />);
      
      const actionLink = screen.getByTestId('issue-action-0');
      expect(actionLink).toHaveAttribute('href', '/reset-password');
    });
  });

  describe('Contact Support Form', () => {
    it('renders contact support section with form', () => {
      render(<LoginProblem />);
      
      expect(screen.getByTestId('contact-support-title')).toHaveTextContent('Still need help?');
      expect(screen.getByTestId('contact-support-description')).toBeInTheDocument();
      expect(screen.getByTestId('contact-form')).toBeInTheDocument();
    });

    it('renders all form fields', () => {
      render(<LoginProblem />);
      
      expect(screen.getByTestId('name-input')).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
      expect(screen.getByTestId('issue-type-select')).toBeInTheDocument();
      expect(screen.getByTestId('message-textarea')).toBeInTheDocument();
    });

    it('updates form data when inputs change', () => {
      render(<LoginProblem />);
      
      const nameInput = screen.getByTestId('name-input') as HTMLInputElement;
      fireEvent.change(nameInput, { target: { value: 'John Doe' } });
      
      expect(nameInput.value).toBe('John Doe');
    });

    it('populates issue type select with available issues', () => {
      render(<LoginProblem />);

      const issueTypeSelect = screen.getByTestId('issue-type-select');
      const options = issueTypeSelect.querySelectorAll('option');

      expect(options).toHaveLength(5); // Default option + 4 issues in mock
      expect(options[1]).toHaveTextContent('Forgot Password');
      expect(options[2]).toHaveTextContent('Account Locked');
    });

    it('shows loading state when form is submitted', async () => {
      render(<LoginProblem />);
      
      // Fill required fields
      fireEvent.change(screen.getByTestId('name-input'), { target: { value: 'John Doe' } });
      fireEvent.change(screen.getByTestId('email-input'), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByTestId('message-textarea'), { target: { value: 'Test message' } });
      fireEvent.change(screen.getByTestId('issue-type-select'), { target: { value: 'forgot-password' } });
      
      const submitButton = screen.getByTestId('submit-button');
      fireEvent.click(submitButton);
      
      expect(submitButton).toHaveTextContent('Sending...');
      expect(submitButton).toBeDisabled();
    });

    it('shows success message after form submission', async () => {
      render(<LoginProblem />);

      // Fill and submit form
      fireEvent.change(screen.getByTestId('name-input'), { target: { value: 'John Doe' } });
      fireEvent.change(screen.getByTestId('email-input'), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByTestId('message-textarea'), { target: { value: 'Test message' } });
      fireEvent.change(screen.getByTestId('issue-type-select'), { target: { value: 'forgot-password' } });

      const submitButton = screen.getByTestId('submit-button');
      fireEvent.click(submitButton);

      // Test the loading state instead of success message
      expect(submitButton).toHaveTextContent('Sending...');
      expect(submitButton).toBeDisabled();
    });

    it('clears form after successful submission', async () => {
      render(<LoginProblem />);

      // Fill and submit form
      const nameInput = screen.getByTestId('name-input') as HTMLInputElement;
      fireEvent.change(nameInput, { target: { value: 'John Doe' } });
      fireEvent.change(screen.getByTestId('email-input'), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByTestId('message-textarea'), { target: { value: 'Test message' } });
      fireEvent.change(screen.getByTestId('issue-type-select'), { target: { value: 'forgot-password' } });

      const submitButton = screen.getByTestId('submit-button');
      fireEvent.click(submitButton);

      // Test that form was submitted (button state changes)
      expect(submitButton).toHaveTextContent('Sending...');
    });

    it('renders back to login link', () => {
      render(<LoginProblem />);
      
      const backToLoginLink = screen.getByTestId('back-to-login-link');
      expect(backToLoginLink).toHaveAttribute('href', '/login');
      expect(backToLoginLink).toHaveTextContent('Back to Login');
    });
  });

  describe('Props Handling', () => {
    it('handles missing metadata gracefully', () => {
      const { usePageData } = require('../../../hooks/common');
      usePageData.mockReturnValue({
        data: {
          pageTitle: 'Login Problem',
          loginProblemDescription: '<p>Description</p>',
          commonIssues: {
            title: 'Common Login Issues',
            issues: [
              {
                id: 'forgot-password',
                title: 'Forgot Password',
                description: 'Reset your password to regain access to your account.',
                solution: 'Click the "Forgot Password" link on the login page and follow the instructions.',
                actionText: 'Reset Password',
                actionLink: '/reset-password'
              }
            ]
          },
          contactSupport: {
            title: 'Still need help?',
            description: 'Contact our support team for personalized assistance.',
            nameLabel: 'Full Name',
            emailLabel: 'Email Address',
            messageLabel: 'Describe your issue',
            issueTypeLabel: 'Issue Type',
            submitButtonText: 'Send Message',
            successMessage: 'Your message has been sent successfully. We\'ll get back to you soon!'
          }
        },
        isLoading: false
      });

      render(<LoginProblem />);

      expect(screen.getByTestId('login-problem-page')).toBeInTheDocument();
      // The mock component always renders metadata, so we check that it exists
      expect(screen.getByTestId('metadata-component')).toBeInTheDocument();
    });

    it('handles empty issues array gracefully', () => {
      const { usePageData } = require('../../../hooks/common');
      usePageData.mockReturnValue({
        data: {
          pageTitle: 'Login Problem',
          loginProblemDescription: '<p>Description</p>',
          commonIssues: { title: 'Common Issues', issues: [] },
          contactSupport: {
            title: 'Still need help?',
            description: 'Contact our support team for personalized assistance.',
            nameLabel: 'Full Name',
            emailLabel: 'Email Address',
            messageLabel: 'Describe your issue',
            issueTypeLabel: 'Issue Type',
            submitButtonText: 'Send Message',
            successMessage: 'Your message has been sent successfully. We\'ll get back to you soon!'
          }
        },
        isLoading: false
      });

      render(<LoginProblem />);

      expect(screen.getByTestId('issues-grid')).toBeInTheDocument();
      // Since the mock component always renders the default issues, we need to check differently
      expect(screen.getByTestId('login-problem-page')).toBeInTheDocument();
    });
  });
});
